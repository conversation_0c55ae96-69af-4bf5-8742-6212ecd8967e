"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Eye,
  ZoomIn,
  ArrowLeft
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface ImageData {
  id: string;
  url: string;
  filename?: string;
  file_path?: string;
}

interface OpcionData {
  id: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen_url?: string;
  imagen_path?: string;
}

interface PreguntaData {
  id: string;
  caso_clinico: string;
  pregunta: string;
  imagen_url?: string;
  imagen_path?: string;
  opciones: OpcionData[];
  respuesta_correcta: string;
  explicacion_general: string;
}

interface CasoClinico {
  id: number;
  title: string;
  especialidad: string;
  sistema: string;
  tema: string;
  descripcion: string;
  images?: ImageData[];
  preguntas: PreguntaData[];
  created_at: string;
}

interface CasosClinicosUIProps {
  casos: CasoClinico[];
  onBack?: () => void;
}

export default function CasosClinicosUI({ casos, onBack }: CasosClinicosUIProps) {
  const [selectedCaso, setSelectedCaso] = useState<CasoClinico | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: number }>({});
  const [showExplanations, setShowExplanations] = useState<{ [key: string]: boolean }>({});
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [quizMode, setQuizMode] = useState(false);

  if (casos.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 text-lg">
          No hay casos clínicos disponibles para esta especialidad.
        </p>
        <p className="text-gray-500 text-sm mt-2">
          Los casos clínicos se mostrarán aquí una vez que sean subidos al sistema.
        </p>
      </div>
    );
  }

  const handleCaseSelect = (caso: CasoClinico) => {
    setSelectedCaso(caso);
    setCurrentQuestionIndex(0);
    setSelectedAnswers({});
    setShowExplanations({});
    setQuizMode(false);
  };

  const handleAnswerSelect = (questionId: string, answerIndex: number) => {
    if (showExplanations[questionId]) return;

    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));

    // Show explanation after a short delay
    setTimeout(() => {
      setShowExplanations(prev => ({
        ...prev,
        [questionId]: true
      }));
    }, 500);
  };

  const resetQuestion = (questionId: string) => {
    setSelectedAnswers(prev => {
      const newAnswers = { ...prev };
      delete newAnswers[questionId];
      return newAnswers;
    });
    setShowExplanations(prev => {
      const newExplanations = { ...prev };
      delete newExplanations[questionId];
      return newExplanations;
    });
  };

  const startQuizMode = () => {
    setQuizMode(true);
    setCurrentQuestionIndex(0);
    setSelectedAnswers({});
    setShowExplanations({});
  };

  const nextQuestion = () => {
    if (selectedCaso && currentQuestionIndex < selectedCaso.preguntas.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const prevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const getCorrectAnswerIndex = (pregunta: PreguntaData): number => {
    return pregunta.opciones.findIndex(opcion => opcion.es_correcta);
  };

  const calculateScore = (): { correct: number; total: number } => {
    if (!selectedCaso) return { correct: 0, total: 0 };

    let correct = 0;
    selectedCaso.preguntas.forEach(pregunta => {
      const selectedAnswer = selectedAnswers[pregunta.id];
      const correctAnswer = getCorrectAnswerIndex(pregunta);
      if (selectedAnswer === correctAnswer) {
        correct++;
      }
    });

    return { correct, total: selectedCaso.preguntas.length };
  };

  // Case list view
  if (!selectedCaso) {
    return (
      <div className="space-y-6">
        {onBack && (
          <Button
            variant="outline"
            onClick={onBack}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver
          </Button>
        )}

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {casos.map((caso) => (
            <Card key={caso.id} className="cursor-pointer hover:shadow-md transition-shadow group">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg font-medium truncate" title={caso.title}>
                      {caso.title}
                    </CardTitle>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {caso.especialidad}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {caso.sistema}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {caso.descripcion}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>{caso.preguntas.length} pregunta{caso.preguntas.length !== 1 ? 's' : ''}</span>
                  <span>{caso.tema}</span>
                </div>
                <Button
                  onClick={() => handleCaseSelect(caso)}
                  className="w-full"
                  size="sm"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Ver caso clínico
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Quiz mode view
  if (quizMode && selectedCaso) {
    const currentQuestion = selectedCaso.preguntas[currentQuestionIndex];
    const selectedAnswer = selectedAnswers[currentQuestion.id];
    const showExplanation = showExplanations[currentQuestion.id];
    const correctAnswerIndex = getCorrectAnswerIndex(currentQuestion);
    const progress = ((currentQuestionIndex + 1) / selectedCaso.preguntas.length) * 100;

    return (
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => setQuizMode(false)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al caso
          </Button>
          <div className="text-sm text-gray-600">
            Pregunta {currentQuestionIndex + 1} de {selectedCaso.preguntas.length}
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-2">
          <Progress value={progress} className="h-2" />
          <div className="text-xs text-gray-500 text-center">
            {Math.round(progress)}% completado
          </div>
        </div>

        {/* Clinical Case Context */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-lg text-blue-800">
              📋 Caso Clínico: {selectedCaso.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-blue-700 leading-relaxed">
              {currentQuestion.caso_clinico}
            </p>
          </CardContent>
        </Card>

        {/* Question */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl leading-relaxed">
              {currentQuestion.pregunta}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Question Image */}
            {currentQuestion.imagen_url && (
              <div className="mb-6">
                <img
                  src={currentQuestion.imagen_url}
                  alt="Imagen de la pregunta"
                  className="w-full max-w-md mx-auto rounded-lg border cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => setSelectedImageUrl(currentQuestion.imagen_url!)}
                />
              </div>
            )}

            {/* Answer Options */}
            <div className="space-y-3">
              {currentQuestion.opciones.map((opcion, index) => {
                const isSelected = selectedAnswer === index;
                const isCorrect = index === correctAnswerIndex;

                let buttonClass = "w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ";

                if (showExplanation) {
                  if (isCorrect) {
                    buttonClass += "bg-green-50 border-green-500 text-green-800";
                  } else if (isSelected) {
                    buttonClass += "bg-red-50 border-red-500 text-red-800";
                  } else {
                    buttonClass += "bg-gray-50 border-gray-200 text-gray-600";
                  }
                } else {
                  if (isSelected) {
                    buttonClass += "bg-blue-50 border-blue-500 text-blue-800";
                  } else {
                    buttonClass += "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50";
                  }
                }

                return (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(currentQuestion.id, index)}
                    disabled={showExplanation}
                    className={buttonClass}
                  >
                    <div className="flex items-start gap-3">
                      <span className="font-semibold text-lg min-w-[24px]">
                        {String.fromCharCode(65 + index)}.
                      </span>
                      <span className="flex-1">{opcion.opcion}</span>
                      {showExplanation && (
                        <div className="ml-auto">
                          {isCorrect ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : isSelected ? (
                            <XCircle className="w-5 h-5 text-red-600" />
                          ) : null}
                        </div>
                      )}
                    </div>

                    {/* Option Image */}
                    {opcion.imagen_url && (
                      <div className="mt-3 ml-7">
                        <img
                          src={opcion.imagen_url}
                          alt={`Opción ${String.fromCharCode(65 + index)}`}
                          className="max-w-xs rounded border cursor-pointer hover:shadow-md transition-shadow"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImageUrl(opcion.imagen_url!);
                          }}
                        />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Explanation */}
            {showExplanation && (
              <div className="mt-6 space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {selectedAnswer === correctAnswerIndex ? "¡Correcto! ✅" : "Incorrecto ❌"}
                  </h4>
                  <p className="text-gray-700 leading-relaxed">
                    {currentQuestion.explicacion_general}
                  </p>
                </div>

                {/* Individual option explanations */}
                {currentQuestion.opciones.map((opcion, index) => {
                  if (!opcion.explicacion) return null;

                  return (
                    <div key={index} className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                      <div className="font-medium text-blue-800 mb-1">
                        Opción {String.fromCharCode(65 + index)}: {opcion.es_correcta ? "Correcta" : "Incorrecta"}
                      </div>
                      <p className="text-blue-700 text-sm">{opcion.explicacion}</p>
                    </div>
                  );
                })}

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => resetQuestion(currentQuestion.id)}
                    size="sm"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reintentar
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={prevQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>

          <div className="text-sm text-gray-600">
            {currentQuestionIndex + 1} / {selectedCaso.preguntas.length}
          </div>

          <Button
            variant="outline"
            onClick={nextQuestion}
            disabled={currentQuestionIndex === selectedCaso.preguntas.length - 1}
          >
            Siguiente
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        {/* Final Score */}
        {Object.keys(selectedAnswers).length === selectedCaso.preguntas.length && (
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-blue-800 mb-2">
                  Resultado Final
                </h3>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {calculateScore().correct} / {calculateScore().total}
                </div>
                <p className="text-blue-700">
                  {Math.round((calculateScore().correct / calculateScore().total) * 100)}% de respuestas correctas
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Detailed case view (non-quiz mode)
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setSelectedCaso(null)}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Volver a casos
        </Button>
        <Button
          onClick={startQuizMode}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Iniciar modo quiz
        </Button>
      </div>

      {/* Case Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap gap-2 mb-3">
            <Badge variant="outline">{selectedCaso.especialidad}</Badge>
            <Badge variant="outline">{selectedCaso.sistema}</Badge>
            <Badge variant="outline">{selectedCaso.tema}</Badge>
          </div>
          <CardTitle className="text-2xl">{selectedCaso.title}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed mb-4">
            {selectedCaso.descripcion}
          </p>

          {/* Case Images */}
          {selectedCaso.images && selectedCaso.images.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800">Imágenes del caso:</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {selectedCaso.images.map((image, index) => (
                  <div key={index} className="relative group cursor-pointer">
                    <img
                      src={image.url}
                      alt={`Caso clínico ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border hover:shadow-md transition-shadow"
                      onClick={() => setSelectedImageUrl(image.url)}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
                      <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-6">
        <h3 className="text-xl font-semibold text-gray-800">
          Preguntas ({selectedCaso.preguntas.length})
        </h3>

        {selectedCaso.preguntas.map((pregunta, questionIndex) => {
          const selectedAnswer = selectedAnswers[pregunta.id];
          const showExplanation = showExplanations[pregunta.id];
          const correctAnswerIndex = getCorrectAnswerIndex(pregunta);

          return (
            <Card key={pregunta.id} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    Pregunta {questionIndex + 1}
                  </span>
                </div>
                <CardTitle className="text-lg leading-relaxed">
                  {pregunta.pregunta}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Question Image */}
                {pregunta.imagen_url && (
                  <div className="mb-4">
                    <img
                      src={pregunta.imagen_url}
                      alt="Imagen de la pregunta"
                      className="max-w-md rounded-lg border cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setSelectedImageUrl(pregunta.imagen_url!)}
                    />
                  </div>
                )}

                {/* Answer Options */}
                <div className="space-y-2 mb-4">
                  {pregunta.opciones.map((opcion, index) => {
                    const isSelected = selectedAnswer === index;
                    const isCorrect = index === correctAnswerIndex;

                    let buttonClass = "w-full p-3 text-left rounded-lg border transition-all ";

                    if (showExplanation) {
                      if (isCorrect) {
                        buttonClass += "bg-green-100 border-green-300 text-green-800";
                      } else if (isSelected) {
                        buttonClass += "bg-red-100 border-red-300 text-red-800";
                      } else {
                        buttonClass += "bg-gray-50 border-gray-200 text-gray-600";
                      }
                    } else {
                      if (isSelected) {
                        buttonClass += "bg-blue-100 border-blue-300 text-blue-800";
                      } else {
                        buttonClass += "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50";
                      }
                    }

                    return (
                      <button
                        key={index}
                        onClick={() => handleAnswerSelect(pregunta.id, index)}
                        disabled={showExplanation}
                        className={buttonClass}
                      >
                        <div className="flex items-start gap-3">
                          <span className="font-semibold min-w-[24px]">
                            {String.fromCharCode(65 + index)}.
                          </span>
                          <span className="flex-1">{opcion.opcion}</span>
                          {showExplanation && (
                            <div className="ml-auto">
                              {isCorrect ? (
                                <CheckCircle className="w-4 h-4 text-green-600" />
                              ) : isSelected ? (
                                <XCircle className="w-4 h-4 text-red-600" />
                              ) : null}
                            </div>
                          )}
                        </div>

                        {/* Option Image */}
                        {opcion.imagen_url && (
                          <div className="mt-2 ml-7">
                            <img
                              src={opcion.imagen_url}
                              alt={`Opción ${String.fromCharCode(65 + index)}`}
                              className="max-w-xs rounded border cursor-pointer hover:shadow-md transition-shadow"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedImageUrl(opcion.imagen_url!);
                              }}
                            />
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>

                {/* Explanation */}
                {showExplanation && (
                  <div className="space-y-3">
                    <div className="p-3 bg-gray-50 rounded border">
                      <h5 className="font-semibold text-gray-800 mb-1">
                        {selectedAnswer === correctAnswerIndex ? "¡Correcto! ✅" : "Incorrecto ❌"}
                      </h5>
                      <p className="text-gray-700 text-sm">{pregunta.explicacion_general}</p>
                    </div>

                    <Button
                      variant="outline"
                      onClick={() => resetQuestion(pregunta.id)}
                      size="sm"
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Reintentar
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Image Modal */}
      {selectedImageUrl && (
        <Dialog open={!!selectedImageUrl} onOpenChange={() => setSelectedImageUrl(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] p-0">
            <DialogHeader className="p-6 pb-0">
              <DialogTitle>Vista de imagen</DialogTitle>
            </DialogHeader>
            <div className="p-6 pt-0">
              <img
                src={selectedImageUrl}
                alt="Vista ampliada"
                className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}