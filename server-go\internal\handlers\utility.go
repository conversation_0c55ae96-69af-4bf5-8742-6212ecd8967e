package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/middleware"
)

// GetEspecialidades handles getting all unique especialidades
func (h *UploadHandler) GetEspecialidades(c *gin.Context) {
	especialidades, err := h.repo.GetUniqueEspecialidades()
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get especialidades", err)
		return
	}

	response := map[string]interface{}{
		"especialidades": especialidades,
	}

	c.<PERSON>(http.StatusOK, response)
}

// GetSistemasByEspecialidad handles getting sistemas for a specific especialidad
func (h *UploadHandler) GetSistemasByEspecialidad(c *gin.Context) {
	especialidad := c.Param("especialidad")
	if especialidad == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad parameter", nil)
		return
	}

	sistemas, err := h.repo.GetSistemasByEspecialidad(especialidad)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get sistemas", err)
		return
	}

	response := map[string]interface{}{
		"sistemas": sistemas,
	}

	c.JSON(http.StatusOK, response)
}

// GetTemasByEspecialidadSistema handles getting temas for a specific especialidad and sistema
func (h *UploadHandler) GetTemasByEspecialidadSistema(c *gin.Context) {
	especialidad := c.Param("especialidad")
	sistema := c.Param("sistema")

	if especialidad == "" || sistema == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad or sistema parameter", nil)
		return
	}

	temas, err := h.repo.GetTemasByEspecialidadSistema(especialidad, sistema)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get temas", err)
		return
	}

	response := map[string]interface{}{
		"temas": temas,
	}

	c.JSON(http.StatusOK, response)
}

// Placeholder handlers for clinical notes, cases, cuestionarios, and flashcards
// These would be implemented similarly to the video handlers above

func (h *UploadHandler) UploadClinicalNotes(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical notes upload not implemented yet", nil)
}

func (h *UploadHandler) ListClinicalNotes(c *gin.Context) {
	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Return empty array for now - can be implemented later
	c.JSON(http.StatusOK, []interface{}{})
}

func (h *UploadHandler) GetClinicalNote(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get clinical note not implemented yet", nil)
}

func (h *UploadHandler) UploadClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical case upload not implemented yet", nil)
}

func (h *UploadHandler) GetClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get clinical case not implemented yet", nil)
}

func (h *UploadHandler) UpdateClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update clinical case not implemented yet", nil)
}

func (h *UploadHandler) DeleteClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Delete clinical case not implemented yet", nil)
}

func (h *UploadHandler) CreateCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Create cuestionario not implemented yet", nil)
}

func (h *UploadHandler) ListCuestionarios(c *gin.Context) {
	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	cuestionarios, err := h.repo.ListCuestionarios()
	if err != nil {
		// Log the error but return empty array instead of error
		fmt.Printf("Warning: Failed to list cuestionarios: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	cuestionariosOut := make([]interface{}, len(cuestionarios))
	for i, cuestionario := range cuestionarios {
		cuestionariosOut[i] = map[string]interface{}{
			"id":           cuestionario.ID,
			"title":        cuestionario.Title,
			"especialidad": cuestionario.Especialidad,
			"sistema":      cuestionario.Sistema,
			"tema":         cuestionario.Tema,
			"created_at":   cuestionario.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, cuestionariosOut)
}

func (h *UploadHandler) GetCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get cuestionario not implemented yet", nil)
}

func (h *UploadHandler) UploadCuestionariosJSON(c *gin.Context) {
	// Parse form data
	jsonContent := c.PostForm("json_content")
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")

	// Validate required fields
	if jsonContent == "" || especialidad == "" || sistema == "" || tema == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	// Parse JSON content
	var jsonData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &jsonData); err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid JSON format", err)
		return
	}

	// Validate JSON structure
	if len(jsonData) == 0 {
		middleware.HandleError(c, http.StatusBadRequest, "JSON must contain at least one cuestionario", nil)
		return
	}

	// Convert to DTO format
	var cuestionarios []dto.CuestionarioJsonCreate
	for _, item := range jsonData {
		// Validate required fields
		requiredFields := []string{"id", "pregunta", "opciones", "respuesta_correcta", "explicacion_general"}
		for _, field := range requiredFields {
			if _, exists := item[field]; !exists {
				middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Missing required field: %s", field), nil)
				return
			}
		}

		// Validate opciones structure
		opciones, ok := item["opciones"].([]interface{})
		if !ok {
			middleware.HandleError(c, http.StatusBadRequest, "Opciones must be an array", nil)
			return
		}

		var opcionesDTO []dto.OpcionCuestionarioJsonCreate
		for _, opcionInterface := range opciones {
			opcion, ok := opcionInterface.(map[string]interface{})
			if !ok {
				middleware.HandleError(c, http.StatusBadRequest, "Each opcion must be an object", nil)
				return
			}

			// Validate opcion fields
			opcionFields := []string{"opcion", "es_correcta", "explicacion"}
			for _, field := range opcionFields {
				if _, exists := opcion[field]; !exists {
					middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Missing required opcion field: %s", field), nil)
					return
				}
			}

			opcionDTO := dto.OpcionCuestionarioJsonCreate{
				Opcion:      opcion["opcion"].(string),
				EsCorrecta:  opcion["es_correcta"].(bool),
				Explicacion: opcion["explicacion"].(string),
			}
			opcionesDTO = append(opcionesDTO, opcionDTO)
		}

		cuestionarioDTO := dto.CuestionarioJsonCreate{
			ID:                 item["id"].(string),
			Pregunta:           item["pregunta"].(string),
			Opciones:           opcionesDTO,
			RespuestaCorrecta:  item["respuesta_correcta"].(string),
			ExplicacionGeneral: item["explicacion_general"].(string),
		}
		cuestionarios = append(cuestionarios, cuestionarioDTO)
	}

	// Create upload payload
	var titlePtr *string
	if titulo != "" {
		titlePtr = &titulo
	}

	payload := &dto.CuestionarioJsonUpload{
		Cuestionarios: cuestionarios,
		Especialidad:  especialidad,
		Sistema:       sistema,
		Tema:          tema,
		Title:         titlePtr,
	}

	// Create cuestionarios
	createdCuestionarios, err := h.repo.CreateCuestionariosFromJSONPayload(payload)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to create cuestionarios", err)
		return
	}

	// Prepare response
	cuestionariosOut := make([]map[string]interface{}, len(createdCuestionarios))
	for i, cuestionario := range createdCuestionarios {
		cuestionariosOut[i] = map[string]interface{}{
			"id":           cuestionario.ID,
			"title":        cuestionario.Title,
			"especialidad": cuestionario.Especialidad,
			"tema":         cuestionario.Tema,
			"created_at":   cuestionario.CreatedAt,
		}
	}

	response := dto.SuccessResponse{
		Success: true,
		Message: fmt.Sprintf("Se crearon %d cuestionarios exitosamente", len(createdCuestionarios)),
		Data: map[string]interface{}{
			"cuestionarios_creados": len(createdCuestionarios),
			"cuestionarios":         cuestionariosOut,
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *UploadHandler) UpdateCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update cuestionario not implemented yet", nil)
}

func (h *UploadHandler) DeleteCuestionario(c *gin.Context) {
	// Get ID from URL parameter
	idStr := c.Param("id")
	if idStr == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing cuestionario ID", nil)
		return
	}

	// Convert ID to uint
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid cuestionario ID", err)
		return
	}

	// Delete cuestionario
	if err := h.repo.DeleteCuestionario(uint(id)); err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to delete cuestionario", err)
		return
	}

	// Return success response
	response := dto.SuccessResponse{
		Success: true,
		Message: "Cuestionario eliminado exitosamente",
		Data:    map[string]interface{}{"id": id},
	}

	c.JSON(http.StatusOK, response)
}

func (h *UploadHandler) CreateFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Create flashcard not implemented yet", nil)
}

func (h *UploadHandler) ListFlashcards(c *gin.Context) {
	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Return empty array for now - can be implemented later
	c.JSON(http.StatusOK, []interface{}{})
}

func (h *UploadHandler) GetFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get flashcard not implemented yet", nil)
}

func (h *UploadHandler) UploadFlashcardsJSON(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Flashcards JSON upload not implemented yet", nil)
}

func (h *UploadHandler) UpdateFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update flashcard not implemented yet", nil)
}

func (h *UploadHandler) DeleteFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Delete flashcard not implemented yet", nil)
}
