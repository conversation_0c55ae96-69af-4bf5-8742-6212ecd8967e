package repository

import (
	"encoding/json"
	"fmt"

	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/models"
)

// UploadContentRepository handles database operations for upload content
type UploadContentRepository struct {
	db *gorm.DB
}

// NewUploadContentRepository creates a new upload content repository
func NewUploadContentRepository(db *gorm.DB) *UploadContentRepository {
	return &UploadContentRepository{db: db}
}

// Videoclases operations
func (r *UploadContentRepository) CreateVideoclase(data *dto.VideoclaseCreate) (*models.VideoclasesData, error) {
	videoclase := &models.VideoclasesData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		URL:          data.URL,
		FilePath:     data.FilePath,
		ThumbnailURL: data.ThumbnailURL,
		Duration:     data.Duration,
		Description:  data.Description,
	}

	if err := r.db.Create(videoclase).Error; err != nil {
		return nil, err
	}

	return videoclase, nil
}

func (r *UploadContentRepository) GetVideoclase(id uint) (*models.VideoclasesData, error) {
	var videoclase models.VideoclasesData
	if err := r.db.First(&videoclase, id).Error; err != nil {
		return nil, err
	}
	return &videoclase, nil
}

func (r *UploadContentRepository) ListVideoclases() ([]models.VideoclasesData, error) {
	var videoclases []models.VideoclasesData
	if err := r.db.Order("created_at DESC").Find(&videoclases).Error; err != nil {
		return nil, err
	}
	return videoclases, nil
}

func (r *UploadContentRepository) UpdateVideoclase(id uint, data *dto.VideoclaseUpdate) (*models.VideoclasesData, error) {
	var videoclase models.VideoclasesData
	if err := r.db.First(&videoclase, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.URL != nil {
		updates["url"] = *data.URL
	}
	if data.FilePath != nil {
		updates["file_path"] = *data.FilePath
	}
	if data.ThumbnailURL != nil {
		updates["thumbnail_url"] = *data.ThumbnailURL
	}
	if data.Duration != nil {
		updates["duration"] = *data.Duration
	}
	if data.Description != nil {
		updates["description"] = *data.Description
	}

	if err := r.db.Model(&videoclase).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &videoclase, nil
}

func (r *UploadContentRepository) DeleteVideoclase(id uint) error {
	return r.db.Delete(&models.VideoclasesData{}, id).Error
}

// Videos Cortos operations
func (r *UploadContentRepository) CreateVideoCorto(data *dto.VideoCortoCreate) (*models.VideosCortosData, error) {
	videoCorto := &models.VideosCortosData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		URL:          data.URL,
		FilePath:     data.FilePath,
		ThumbnailURL: data.ThumbnailURL,
		Duration:     data.Duration,
		Description:  data.Description,
	}

	if err := r.db.Create(videoCorto).Error; err != nil {
		return nil, err
	}

	return videoCorto, nil
}

func (r *UploadContentRepository) GetVideoCorto(id uint) (*models.VideosCortosData, error) {
	var videoCorto models.VideosCortosData
	if err := r.db.First(&videoCorto, id).Error; err != nil {
		return nil, err
	}
	return &videoCorto, nil
}

func (r *UploadContentRepository) ListVideosCortos() ([]models.VideosCortosData, error) {
	var videosCortos []models.VideosCortosData
	if err := r.db.Order("created_at DESC").Find(&videosCortos).Error; err != nil {
		return nil, err
	}
	return videosCortos, nil
}

func (r *UploadContentRepository) UpdateVideoCorto(id uint, data *dto.VideoCortoUpdate) (*models.VideosCortosData, error) {
	var videoCorto models.VideosCortosData
	if err := r.db.First(&videoCorto, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.URL != nil {
		updates["url"] = *data.URL
	}
	if data.FilePath != nil {
		updates["file_path"] = *data.FilePath
	}
	if data.ThumbnailURL != nil {
		updates["thumbnail_url"] = *data.ThumbnailURL
	}
	if data.Duration != nil {
		updates["duration"] = *data.Duration
	}
	if data.Description != nil {
		updates["description"] = *data.Description
	}

	if err := r.db.Model(&videoCorto).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &videoCorto, nil
}

func (r *UploadContentRepository) DeleteVideoCorto(id uint) error {
	return r.db.Delete(&models.VideosCortosData{}, id).Error
}

// Notas Clinicas operations
func (r *UploadContentRepository) CreateNotaClinica(data *dto.NotaClinicaCreate, imagesData []map[string]interface{}) (*models.NotasClinicasData, error) {
	// Convert images to JSON
	var imagesJSON []byte
	var err error
	if len(imagesData) > 0 {
		imagesJSON, err = json.Marshal(imagesData)
		if err != nil {
			return nil, err
		}
	}

	notaClinica := &models.NotasClinicasData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		Content:      data.Content,
		Images:       imagesJSON,
	}

	if err := r.db.Create(notaClinica).Error; err != nil {
		return nil, err
	}

	return notaClinica, nil
}

func (r *UploadContentRepository) GetNotaClinica(id uint) (*models.NotasClinicasData, error) {
	var notaClinica models.NotasClinicasData
	if err := r.db.First(&notaClinica, id).Error; err != nil {
		return nil, err
	}
	return &notaClinica, nil
}

func (r *UploadContentRepository) ListNotasClinicas() ([]models.NotasClinicasData, error) {
	var notasClinicas []models.NotasClinicasData
	if err := r.db.Order("created_at DESC").Find(&notasClinicas).Error; err != nil {
		return nil, err
	}
	return notasClinicas, nil
}

func (r *UploadContentRepository) UpdateNotaClinica(id uint, data *dto.NotaClinicaUpdate) (*models.NotasClinicasData, error) {
	var notaClinica models.NotasClinicasData
	if err := r.db.First(&notaClinica, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.Content != nil {
		updates["content"] = *data.Content
	}
	if data.Images != nil {
		imagesJSON, err := json.Marshal(*data.Images)
		if err != nil {
			return nil, err
		}
		updates["images"] = imagesJSON
	}

	if err := r.db.Model(&notaClinica).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &notaClinica, nil
}

func (r *UploadContentRepository) DeleteNotaClinica(id uint) error {
	return r.db.Delete(&models.NotasClinicasData{}, id).Error
}

// Casos Clinicos operations
func (r *UploadContentRepository) CreateCasoClinico(data *dto.CasoClinicoCreate, imagesData []map[string]interface{}, preguntasData []map[string]interface{}) (*models.CasosClinicosData, error) {
	// Convert images to JSON
	var imagesJSON []byte
	var err error
	if len(imagesData) > 0 {
		imagesJSON, err = json.Marshal(imagesData)
		if err != nil {
			return nil, err
		}
	}

	// Convert preguntas to JSON
	preguntasJSON, err := json.Marshal(preguntasData)
	if err != nil {
		return nil, err
	}

	casoClinico := &models.CasosClinicosData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		Descripcion:  data.Descripcion,
		Images:       imagesJSON,
		Preguntas:    preguntasJSON,
	}

	if err := r.db.Create(casoClinico).Error; err != nil {
		return nil, err
	}

	return casoClinico, nil
}

func (r *UploadContentRepository) GetCasoClinico(id uint) (*models.CasosClinicosData, error) {
	var casoClinico models.CasosClinicosData
	if err := r.db.First(&casoClinico, id).Error; err != nil {
		return nil, err
	}
	return &casoClinico, nil
}

func (r *UploadContentRepository) ListCasosClinicosFromJSON(especialidad, sistema, tema string, casosData []map[string]interface{}) ([]models.CasosClinicosData, error) {
	var createdCasos []models.CasosClinicosData

	for _, casoData := range casosData {
		// Convert caso data to proper format
		preguntasData := []map[string]interface{}{
			{
				"caso_clinico":        casoData["caso_clinico"],
				"pregunta":            casoData["pregunta"],
				"respuesta_correcta":  casoData["respuesta_correcta"],
				"explicacion_general": casoData["explicacion_general"],
				"opciones":            casoData["opciones"],
			},
		}

		preguntasJSON, err := json.Marshal(preguntasData)
		if err != nil {
			return nil, err
		}

		title := "Caso Clínico"
		if id, ok := casoData["id"].(string); ok {
			title = "Caso Clínico " + id
		}

		casoClinico := models.CasosClinicosData{
			Title:        title,
			Especialidad: especialidad,
			Sistema:      sistema,
			Tema:         tema,
			Descripcion:  casoData["caso_clinico"].(string),
			Images:       []byte("[]"),
			Preguntas:    preguntasJSON,
		}

		if err := r.db.Create(&casoClinico).Error; err != nil {
			return nil, err
		}

		createdCasos = append(createdCasos, casoClinico)
	}

	return createdCasos, nil
}

func (r *UploadContentRepository) ListCasosClinicosData() ([]models.CasosClinicosData, error) {
	var casosClinicosData []models.CasosClinicosData
	if err := r.db.Order("created_at DESC").Find(&casosClinicosData).Error; err != nil {
		return nil, err
	}
	return casosClinicosData, nil
}

func (r *UploadContentRepository) UpdateCasoClinico(id uint, data *dto.CasoClinicoUpdate) (*models.CasosClinicosData, error) {
	var casoClinico models.CasosClinicosData
	if err := r.db.First(&casoClinico, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.Descripcion != nil {
		updates["descripcion"] = *data.Descripcion
	}
	if data.Images != nil {
		imagesJSON, err := json.Marshal(*data.Images)
		if err != nil {
			return nil, err
		}
		updates["images"] = imagesJSON
	}
	if data.Preguntas != nil {
		preguntasJSON, err := json.Marshal(*data.Preguntas)
		if err != nil {
			return nil, err
		}
		updates["preguntas"] = preguntasJSON
	}

	if err := r.db.Model(&casoClinico).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &casoClinico, nil
}

func (r *UploadContentRepository) DeleteCasoClinico(id uint) error {
	return r.db.Delete(&models.CasosClinicosData{}, id).Error
}

// Cuestionarios operations
func (r *UploadContentRepository) CreateCuestionario(data *dto.CuestionarioCreate) (*models.CuestionariosData, error) {
	// Convert preguntas to JSON
	preguntasJSON, err := json.Marshal(data.Preguntas)
	if err != nil {
		return nil, err
	}

	cuestionario := &models.CuestionariosData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		Preguntas:    preguntasJSON,
	}

	if err := r.db.Create(cuestionario).Error; err != nil {
		return nil, err
	}

	return cuestionario, nil
}

func (r *UploadContentRepository) CreateCuestionariosFromJSON(especialidad, sistema, tema string, cuestionariosData []map[string]interface{}) ([]models.CuestionariosData, error) {
	var createdCuestionarios []models.CuestionariosData

	for _, cuestionarioData := range cuestionariosData {
		// Convert cuestionario data to proper format
		preguntasData := []map[string]interface{}{
			{
				"pregunta":            cuestionarioData["pregunta"],
				"respuesta_correcta":  cuestionarioData["respuesta_correcta"],
				"explicacion_general": cuestionarioData["explicacion_general"],
				"opciones":            cuestionarioData["opciones"],
			},
		}

		preguntasJSON, err := json.Marshal(preguntasData)
		if err != nil {
			return nil, err
		}

		title := "Cuestionario"
		if id, ok := cuestionarioData["id"].(string); ok {
			title = "Cuestionario " + id
		}

		cuestionario := models.CuestionariosData{
			Title:        title,
			Especialidad: especialidad,
			Sistema:      sistema,
			Tema:         tema,
			Preguntas:    preguntasJSON,
		}

		if err := r.db.Create(&cuestionario).Error; err != nil {
			return nil, err
		}

		createdCuestionarios = append(createdCuestionarios, cuestionario)
	}

	return createdCuestionarios, nil
}

func (r *UploadContentRepository) GetCuestionario(id uint) (*models.CuestionariosData, error) {
	var cuestionario models.CuestionariosData
	if err := r.db.First(&cuestionario, id).Error; err != nil {
		return nil, err
	}
	return &cuestionario, nil
}

func (r *UploadContentRepository) ListCuestionarios() ([]models.CuestionariosData, error) {
	var cuestionarios []models.CuestionariosData
	if err := r.db.Order("created_at DESC").Find(&cuestionarios).Error; err != nil {
		return nil, err
	}
	return cuestionarios, nil
}

func (r *UploadContentRepository) UpdateCuestionario(id uint, data *dto.CuestionarioUpdate) (*models.CuestionariosData, error) {
	var cuestionario models.CuestionariosData
	if err := r.db.First(&cuestionario, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.Preguntas != nil {
		preguntasJSON, err := json.Marshal(*data.Preguntas)
		if err != nil {
			return nil, err
		}
		updates["preguntas"] = preguntasJSON
	}

	if err := r.db.Model(&cuestionario).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &cuestionario, nil
}

func (r *UploadContentRepository) DeleteCuestionario(id uint) error {
	return r.db.Delete(&models.CuestionariosData{}, id).Error
}

// Flashcards operations
func (r *UploadContentRepository) CreateFlashcard(data *dto.FlashcardCreate) (*models.FlashcardsData, error) {
	// Convert etiquetas to JSON
	var etiquetasJSON []byte
	var err error
	if len(data.Etiquetas) > 0 {
		etiquetasJSON, err = json.Marshal(data.Etiquetas)
		if err != nil {
			return nil, err
		}
	}

	flashcard := &models.FlashcardsData{
		Title:        data.Title,
		Especialidad: data.Especialidad,
		Sistema:      data.Sistema,
		Tema:         data.Tema,
		Pregunta:     data.Pregunta,
		Respuesta:    data.Respuesta,
		Etiquetas:    etiquetasJSON,
	}

	if err := r.db.Create(flashcard).Error; err != nil {
		return nil, err
	}

	return flashcard, nil
}

func (r *UploadContentRepository) CreateFlashcardsFromJSON(especialidad, sistema, tema, title string, flashcardsData []map[string]interface{}) ([]models.FlashcardsData, error) {
	var createdFlashcards []models.FlashcardsData

	for _, flashcardData := range flashcardsData {
		var etiquetasJSON []byte
		var err error

		if etiquetas, ok := flashcardData["etiquetas"].([]interface{}); ok && len(etiquetas) > 0 {
			// Convert interface{} slice to string slice
			etiquetasStr := make([]string, len(etiquetas))
			for i, etiqueta := range etiquetas {
				if str, ok := etiqueta.(string); ok {
					etiquetasStr[i] = str
				}
			}
			etiquetasJSON, err = json.Marshal(etiquetasStr)
			if err != nil {
				return nil, err
			}
		}

		flashcardTitle := title
		if flashcardTitle == "" {
			flashcardTitle = "Flashcard - " + especialidad + " - " + sistema + " - " + tema
		}

		flashcard := models.FlashcardsData{
			Title:        flashcardTitle,
			Especialidad: especialidad,
			Sistema:      sistema,
			Tema:         tema,
			Pregunta:     flashcardData["pregunta"].(string),
			Respuesta:    flashcardData["respuesta"].(string),
			Etiquetas:    etiquetasJSON,
		}

		if err := r.db.Create(&flashcard).Error; err != nil {
			return nil, err
		}

		createdFlashcards = append(createdFlashcards, flashcard)
	}

	return createdFlashcards, nil
}

func (r *UploadContentRepository) GetFlashcard(id uint) (*models.FlashcardsData, error) {
	var flashcard models.FlashcardsData
	if err := r.db.First(&flashcard, id).Error; err != nil {
		return nil, err
	}
	return &flashcard, nil
}

func (r *UploadContentRepository) ListFlashcards() ([]models.FlashcardsData, error) {
	var flashcards []models.FlashcardsData
	if err := r.db.Order("created_at DESC").Find(&flashcards).Error; err != nil {
		return nil, err
	}
	return flashcards, nil
}

func (r *UploadContentRepository) UpdateFlashcard(id uint, data *dto.FlashcardUpdate) (*models.FlashcardsData, error) {
	var flashcard models.FlashcardsData
	if err := r.db.First(&flashcard, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.Title != nil {
		updates["title"] = *data.Title
	}
	if data.Especialidad != nil {
		updates["especialidad"] = *data.Especialidad
	}
	if data.Sistema != nil {
		updates["sistema"] = *data.Sistema
	}
	if data.Tema != nil {
		updates["tema"] = *data.Tema
	}
	if data.Pregunta != nil {
		updates["pregunta"] = *data.Pregunta
	}
	if data.Respuesta != nil {
		updates["respuesta"] = *data.Respuesta
	}
	if data.Etiquetas != nil {
		etiquetasJSON, err := json.Marshal(*data.Etiquetas)
		if err != nil {
			return nil, err
		}
		updates["etiquetas"] = etiquetasJSON
	}

	if err := r.db.Model(&flashcard).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &flashcard, nil
}

func (r *UploadContentRepository) DeleteFlashcard(id uint) error {
	return r.db.Delete(&models.FlashcardsData{}, id).Error
}

// Utility functions
func (r *UploadContentRepository) GetUniqueEspecialidades() ([]string, error) {
	var especialidades []string

	// Get from all content tables
	tables := []string{
		"videoclases_data", "videos_cortos_data", "notas_clinicas_data",
		"casos_clinicos_data", "cuestionarios_data", "flashcards_data", "repaso_datos",
	}

	especialidadesSet := make(map[string]bool)

	for _, table := range tables {
		var results []string
		if err := r.db.Table(table).Distinct("especialidad").Pluck("especialidad", &results).Error; err != nil {
			return nil, err
		}
		for _, especialidad := range results {
			especialidadesSet[especialidad] = true
		}
	}

	// Convert set to slice
	for especialidad := range especialidadesSet {
		especialidades = append(especialidades, especialidad)
	}

	return especialidades, nil
}

func (r *UploadContentRepository) GetSistemasByEspecialidad(especialidad string) ([]string, error) {
	var sistemas []string

	// Get from all content tables
	tables := []string{
		"videoclases_data", "videos_cortos_data", "notas_clinicas_data",
		"casos_clinicos_data", "cuestionarios_data", "flashcards_data", "repaso_datos",
	}

	sistemasSet := make(map[string]bool)

	for _, table := range tables {
		var results []string
		if err := r.db.Table(table).Where("especialidad = ?", especialidad).Distinct("sistema").Pluck("sistema", &results).Error; err != nil {
			return nil, err
		}
		for _, sistema := range results {
			sistemasSet[sistema] = true
		}
	}

	// Convert set to slice
	for sistema := range sistemasSet {
		sistemas = append(sistemas, sistema)
	}

	return sistemas, nil
}

func (r *UploadContentRepository) GetTemasByEspecialidadSistema(especialidad, sistema string) ([]string, error) {
	var temas []string

	// Get from all content tables
	tables := []string{
		"videoclases_data", "videos_cortos_data", "notas_clinicas_data",
		"casos_clinicos_data", "cuestionarios_data", "flashcards_data", "repaso_datos",
	}

	temasSet := make(map[string]bool)

	for _, table := range tables {
		var results []string
		if err := r.db.Table(table).Where("especialidad = ? AND sistema = ?", especialidad, sistema).Distinct("tema").Pluck("tema", &results).Error; err != nil {
			return nil, err
		}
		for _, tema := range results {
			temasSet[tema] = true
		}
	}

	// Convert set to slice
	for tema := range temasSet {
		temas = append(temas, tema)
	}

	return temas, nil
}

// Content filtering methods for frontend consumption

// GetVideoclasesByFilter gets videoclases with optional filters
func (r *UploadContentRepository) GetVideoclasesByFilter(especialidad, sistema, tema string) ([]models.VideoclasesData, error) {
	query := r.db.Model(&models.VideoclasesData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var videoclases []models.VideoclasesData
	if err := query.Order("created_at DESC").Find(&videoclases).Error; err != nil {
		return nil, err
	}

	return videoclases, nil
}

// GetVideosCortosbyFilter gets videos cortos with optional filters
func (r *UploadContentRepository) GetVideosCortosbyFilter(especialidad, sistema, tema string) ([]models.VideosCortosData, error) {
	query := r.db.Model(&models.VideosCortosData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var videosCortos []models.VideosCortosData
	if err := query.Order("created_at DESC").Find(&videosCortos).Error; err != nil {
		return nil, err
	}

	return videosCortos, nil
}

// GetNotasClinicasByFilter gets notas clinicas with optional filters
func (r *UploadContentRepository) GetNotasClinicasByFilter(especialidad, sistema, tema string) ([]models.NotasClinicasData, error) {
	query := r.db.Model(&models.NotasClinicasData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var notasClinicas []models.NotasClinicasData
	if err := query.Order("created_at DESC").Find(&notasClinicas).Error; err != nil {
		return nil, err
	}

	return notasClinicas, nil
}

// GetCasosClinicosByFilter gets casos clinicos with optional filters
func (r *UploadContentRepository) GetCasosClinicosByFilter(especialidad, sistema, tema string) ([]models.CasosClinicosData, error) {
	query := r.db.Model(&models.CasosClinicosData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var casosClinicosData []models.CasosClinicosData
	if err := query.Order("created_at DESC").Find(&casosClinicosData).Error; err != nil {
		return nil, err
	}

	return casosClinicosData, nil
}

// GetCuestionariosByFilter gets cuestionarios with optional filters
func (r *UploadContentRepository) GetCuestionariosByFilter(especialidad, sistema, tema string) ([]models.CuestionariosData, error) {
	query := r.db.Model(&models.CuestionariosData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var cuestionarios []models.CuestionariosData
	if err := query.Order("created_at DESC").Find(&cuestionarios).Error; err != nil {
		return nil, err
	}

	return cuestionarios, nil
}

// GetFlashcardsByFilter gets flashcards with optional filters
func (r *UploadContentRepository) GetFlashcardsByFilter(especialidad, sistema, tema string) ([]models.FlashcardsData, error) {
	query := r.db.Model(&models.FlashcardsData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var flashcards []models.FlashcardsData
	if err := query.Order("created_at DESC").Find(&flashcards).Error; err != nil {
		return nil, err
	}

	return flashcards, nil
}

// GetRepasoByFilter gets repaso content with optional filters
func (r *UploadContentRepository) GetRepasoByFilter(especialidad, sistema, tema string) ([]models.RepasoData, error) {
	query := r.db.Model(&models.RepasoData{})

	if especialidad != "" {
		query = query.Where("especialidad = ?", especialidad)
	}
	if sistema != "" {
		query = query.Where("sistema = ?", sistema)
	}
	if tema != "" {
		query = query.Where("tema = ?", tema)
	}

	var repaso []models.RepasoData
	if err := query.Order("created_at DESC").Find(&repaso).Error; err != nil {
		return nil, err
	}

	return repaso, nil
}

// CreateCuestionariosFromJSONPayload creates cuestionarios from JSON upload payload
func (r *UploadContentRepository) CreateCuestionariosFromJSONPayload(payload *dto.CuestionarioJsonUpload) ([]models.CuestionariosData, error) {
	var createdCuestionarios []models.CuestionariosData

	for _, cuestionarioJSON := range payload.Cuestionarios {
		// Convert JSON structure to embedded format
		preguntasData := []map[string]interface{}{
			{
				"pregunta":            cuestionarioJSON.Pregunta,
				"respuesta_correcta":  cuestionarioJSON.RespuestaCorrecta,
				"explicacion_general": cuestionarioJSON.ExplicacionGeneral,
				"opciones": func() []map[string]interface{} {
					opciones := make([]map[string]interface{}, len(cuestionarioJSON.Opciones))
					for i, opcion := range cuestionarioJSON.Opciones {
						opciones[i] = map[string]interface{}{
							"opcion":      opcion.Opcion,
							"es_correcta": opcion.EsCorrecta,
							"explicacion": opcion.Explicacion,
						}
					}
					return opciones
				}(),
			},
		}

		// Convert to JSON
		preguntasJSON, err := json.Marshal(preguntasData)
		if err != nil {
			return nil, fmt.Errorf("error marshaling preguntas: %v", err)
		}

		title := payload.Title
		if title == nil || *title == "" {
			defaultTitle := fmt.Sprintf("Cuestionario %s", cuestionarioJSON.ID)
			title = &defaultTitle
		}

		cuestionario := models.CuestionariosData{
			Title:        *title,
			Especialidad: payload.Especialidad,
			Sistema:      payload.Sistema,
			Tema:         payload.Tema,
			Preguntas:    preguntasJSON,
		}

		if err := r.db.Create(&cuestionario).Error; err != nil {
			return nil, err
		}

		createdCuestionarios = append(createdCuestionarios, cuestionario)
	}

	return createdCuestionarios, nil
}
