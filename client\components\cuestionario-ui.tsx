"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Eye,
  ArrowLeft,
  BookOpen
} from "lucide-react";

interface OpcionData {
  id?: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen_url?: string;
  imagen_path?: string;
}

interface CuestionarioPreguntaData {
  id?: string;
  pregunta: string;
  respuesta_correcta: string;
  explicacion_general: string;
  imagen_url?: string;
  imagen_path?: string;
  opciones: OpcionData[];
}

interface Cuestionario {
  id: number;
  title: string;
  especialidad: string;
  sistema: string;
  tema: string;
  preguntas: CuestionarioPreguntaData[];
  created_at: string;
}

interface CuestionarioUIProps {
  cuestionarios: Cuestionario[];
  onBack?: () => void;
}

export default function CuestionarioUI({ cuestionarios, onBack }: CuestionarioUIProps) {
  const [selectedCuestionario, setSelectedCuestionario] = useState<Cuestionario | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: number }>({});
  const [showExplanations, setShowExplanations] = useState<{ [key: string]: boolean }>({});
  const [quizMode, setQuizMode] = useState(false);

  if (cuestionarios.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 text-lg">
          No hay cuestionarios disponibles para esta especialidad.
        </p>
        <p className="text-gray-500 text-sm mt-2">
          Los cuestionarios se mostrarán aquí una vez que sean subidos al sistema.
        </p>
      </div>
    );
  }

  const handleCuestionarioSelect = (cuestionario: Cuestionario) => {
    setSelectedCuestionario(cuestionario);
    setCurrentQuestionIndex(0);
    setSelectedAnswers({});
    setShowExplanations({});
    setQuizMode(false);
  };

  const handleAnswerSelect = (questionId: string, answerIndex: number) => {
    if (showExplanations[questionId]) return;

    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));

    // Show explanation after a short delay
    setTimeout(() => {
      setShowExplanations(prev => ({
        ...prev,
        [questionId]: true
      }));
    }, 500);
  };

  const resetQuestion = (questionId: string) => {
    setSelectedAnswers(prev => {
      const newAnswers = { ...prev };
      delete newAnswers[questionId];
      return newAnswers;
    });
    setShowExplanations(prev => {
      const newExplanations = { ...prev };
      delete newExplanations[questionId];
      return newExplanations;
    });
  };

  const startQuizMode = () => {
    setQuizMode(true);
    setCurrentQuestionIndex(0);
    setSelectedAnswers({});
    setShowExplanations({});
  };

  const nextQuestion = () => {
    if (selectedCuestionario && currentQuestionIndex < selectedCuestionario.preguntas.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const prevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const getCorrectAnswerIndex = (pregunta: CuestionarioPreguntaData): number => {
    return pregunta.opciones.findIndex((opcion: OpcionData) => opcion.es_correcta);
  };

  const calculateScore = (): { correct: number; total: number } => {
    if (!selectedCuestionario) return { correct: 0, total: 0 };

    let correct = 0;
    selectedCuestionario.preguntas.forEach((pregunta, index) => {
      const questionId = pregunta.id || `pregunta-${index}`;
      const selectedAnswer = selectedAnswers[questionId];
      const correctAnswer = getCorrectAnswerIndex(pregunta);
      if (selectedAnswer === correctAnswer) {
        correct++;
      }
    });

    return { correct, total: selectedCuestionario.preguntas.length };
  };

  // Cuestionarios list view
  if (!selectedCuestionario) {
    return (
      <div className="space-y-6">
        {onBack && (
          <Button
            variant="outline"
            onClick={onBack}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver
          </Button>
        )}

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {cuestionarios.map((cuestionario) => (
            <Card key={cuestionario.id} className="cursor-pointer hover:shadow-md transition-shadow group">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg font-medium truncate" title={cuestionario.title}>
                      {cuestionario.title}
                    </CardTitle>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {cuestionario.especialidad}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {cuestionario.sistema}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>{cuestionario.preguntas.length} pregunta{cuestionario.preguntas.length !== 1 ? 's' : ''}</span>
                  <span>{cuestionario.tema}</span>
                </div>
                <Button
                  onClick={() => handleCuestionarioSelect(cuestionario)}
                  className="w-full"
                  size="sm"
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Ver cuestionario
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Quiz mode view
  if (quizMode && selectedCuestionario) {
    const currentQuestion = selectedCuestionario.preguntas[currentQuestionIndex];
    const questionId = currentQuestion.id || `pregunta-${currentQuestionIndex}`;
    const selectedAnswer = selectedAnswers[questionId];
    const showExplanation = showExplanations[questionId];
    const correctAnswerIndex = getCorrectAnswerIndex(currentQuestion);
    const progress = ((currentQuestionIndex + 1) / selectedCuestionario.preguntas.length) * 100;

    return (
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => setQuizMode(false)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al cuestionario
          </Button>
          <div className="text-sm text-gray-600">
            Pregunta {currentQuestionIndex + 1} de {selectedCuestionario.preguntas.length}
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-2">
          <Progress value={progress} className="h-2" />
          <div className="text-xs text-gray-500 text-center">
            {Math.round(progress)}% completado
          </div>
        </div>

        {/* Question */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl leading-relaxed">
              {currentQuestion.pregunta}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Question Image */}
            {currentQuestion.imagen_url && (
              <div className="mb-6">
                <img
                  src={currentQuestion.imagen_url}
                  alt="Imagen de la pregunta"
                  className="w-full max-w-md mx-auto rounded-lg border cursor-pointer hover:shadow-md transition-shadow"
                />
              </div>
            )}

            {/* Answer Options */}
            <div className="space-y-3">
              {currentQuestion.opciones.map((opcion: OpcionData, index) => {
                const isSelected = selectedAnswer === index;
                const isCorrect = index === correctAnswerIndex;

                let buttonClass = "w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ";

                if (showExplanation) {
                  if (isCorrect) {
                    buttonClass += "bg-green-50 border-green-500 text-green-800";
                  } else if (isSelected) {
                    buttonClass += "bg-red-50 border-red-500 text-red-800";
                  } else {
                    buttonClass += "bg-gray-50 border-gray-200 text-gray-600";
                  }
                } else {
                  if (isSelected) {
                    buttonClass += "bg-blue-50 border-blue-500 text-blue-800";
                  } else {
                    buttonClass += "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50";
                  }
                }

                return (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(questionId, index)}
                    disabled={showExplanation}
                    className={buttonClass}
                  >
                    <div className="flex items-start gap-3">
                      <span className="font-semibold text-lg min-w-[24px]">
                        {String.fromCharCode(65 + index)}.
                      </span>
                      <span className="flex-1">{opcion.opcion}</span>
                      {showExplanation && (
                        <div className="ml-auto">
                          {isCorrect ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : isSelected ? (
                            <XCircle className="w-5 h-5 text-red-600" />
                          ) : null}
                        </div>
                      )}
                    </div>

                    {/* Option Image */}
                    {opcion.imagen_url && (
                      <div className="mt-3 ml-7">
                        <img
                          src={opcion.imagen_url}
                          alt={`Opción ${String.fromCharCode(65 + index)}`}
                          className="max-w-xs rounded border cursor-pointer hover:shadow-md transition-shadow"
                        />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Explanation */}
        {showExplanation && (
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-semibold text-gray-800 mb-2">
                {selectedAnswer === correctAnswerIndex ? "¡Correcto! ✅" : "Incorrecto ❌"}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {currentQuestion.explicacion_general}
              </p>
            </div>

            {/* Individual option explanations */}
            {currentQuestion.opciones.map((opcion: OpcionData, index) => {
              if (!opcion.explicacion) return null;

              return (
                <div key={index} className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                  <div className="font-medium text-blue-800 mb-1">
                    Opción {String.fromCharCode(65 + index)}: {opcion.es_correcta ? "Correcta" : "Incorrecta"}
                  </div>
                  <p className="text-blue-700 text-sm">{opcion.explicacion}</p>
                </div>
              );
            })}

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => resetQuestion(questionId)}
                size="sm"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reintentar
              </Button>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={prevQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>

          <div className="text-sm text-gray-600">
            {currentQuestionIndex + 1} / {selectedCuestionario.preguntas.length}
          </div>

          <Button
            variant="outline"
            onClick={nextQuestion}
            disabled={currentQuestionIndex === selectedCuestionario.preguntas.length - 1}
          >
            Siguiente
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        {/* Final Score */}
        {Object.keys(selectedAnswers).length === selectedCuestionario.preguntas.length && (
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-blue-800 mb-2">
                  Resultado Final
                </h3>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {calculateScore().correct} / {calculateScore().total}
                </div>
                <p className="text-blue-700">
                  {Math.round((calculateScore().correct / calculateScore().total) * 100)}% de respuestas correctas
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Detailed cuestionario view (non-quiz mode)
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setSelectedCuestionario(null)}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Volver a cuestionarios
        </Button>
        <Button
          onClick={startQuizMode}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Iniciar modo quiz
        </Button>
      </div>

      {/* Cuestionario Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap gap-2 mb-3">
            <Badge variant="outline">{selectedCuestionario.especialidad}</Badge>
            <Badge variant="outline">{selectedCuestionario.sistema}</Badge>
            <Badge variant="outline">{selectedCuestionario.tema}</Badge>
          </div>
          <CardTitle className="text-2xl">{selectedCuestionario.title}</CardTitle>
        </CardHeader>
      </Card>

      {/* Questions */}
      <div className="space-y-6">
        <h3 className="text-xl font-semibold text-gray-800">
          Preguntas ({selectedCuestionario.preguntas.length})
        </h3>

        {selectedCuestionario.preguntas.map((pregunta, questionIndex) => {
          const questionId = pregunta.id || `pregunta-${questionIndex}`;
          const selectedAnswer = selectedAnswers[questionId];
          const showExplanation = showExplanations[questionId];
          const correctAnswerIndex = getCorrectAnswerIndex(pregunta);

          return (
            <Card key={questionId} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    Pregunta {questionIndex + 1}
                  </span>
                </div>
                <CardTitle className="text-lg leading-relaxed">
                  {pregunta.pregunta}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Question Image */}
                {pregunta.imagen_url && (
                  <div className="mb-4">
                    <img
                      src={pregunta.imagen_url}
                      alt="Imagen de la pregunta"
                      className="max-w-md rounded-lg border cursor-pointer hover:shadow-md transition-shadow"
                    />
                  </div>
                )}

                {/* Answer Options */}
                <div className="space-y-2 mb-4">
                  {pregunta.opciones.map((opcion: OpcionData, index) => {
                    const isSelected = selectedAnswer === index;
                    const isCorrect = index === correctAnswerIndex;

                    let buttonClass = "w-full p-3 text-left rounded-lg border transition-all ";

                    if (showExplanation) {
                      if (isCorrect) {
                        buttonClass += "bg-green-100 border-green-300 text-green-800";
                      } else if (isSelected) {
                        buttonClass += "bg-red-100 border-red-300 text-red-800";
                      } else {
                        buttonClass += "bg-gray-50 border-gray-200 text-gray-600";
                      }
                    } else {
                      if (isSelected) {
                        buttonClass += "bg-blue-100 border-blue-300 text-blue-800";
                      } else {
                        buttonClass += "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50";
                      }
                    }

                    return (
                      <button
                        key={index}
                        onClick={() => handleAnswerSelect(questionId, index)}
                        disabled={showExplanation}
                        className={buttonClass}
                      >
                        <div className="flex items-start gap-3">
                          <span className="font-semibold min-w-[24px]">
                            {String.fromCharCode(65 + index)}.
                          </span>
                          <span className="flex-1">{opcion.opcion}</span>
                          {showExplanation && (
                            <div className="ml-auto">
                              {isCorrect ? (
                                <CheckCircle className="w-4 h-4 text-green-600" />
                              ) : isSelected ? (
                                <XCircle className="w-4 h-4 text-red-600" />
                              ) : null}
                            </div>
                          )}
                        </div>

                        {/* Option Image */}
                        {opcion.imagen_url && (
                          <div className="mt-2 ml-7">
                            <img
                              src={opcion.imagen_url}
                              alt={`Opción ${String.fromCharCode(65 + index)}`}
                              className="max-w-xs rounded border cursor-pointer hover:shadow-md transition-shadow"
                            />
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>

                {/* Explanation */}
                {showExplanation && (
                  <div className="space-y-3">
                    <div className="p-3 bg-gray-50 rounded border">
                      <h5 className="font-semibold text-gray-800 mb-1">
                        {selectedAnswer === correctAnswerIndex ? "¡Correcto! ✅" : "Incorrecto ❌"}
                      </h5>
                      <p className="text-gray-700 text-sm">{pregunta.explicacion_general}</p>
                    </div>

                    <Button
                      variant="outline"
                      onClick={() => resetQuestion(questionId)}
                      size="sm"
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Reintentar
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}