"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/cuestionario.tsx":
/*!*************************************!*\
  !*** ./app/upload/cuestionario.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CuestionarioComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Helper function to get API base URL\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nfunction CuestionarioComponent(param) {\n    let { sharedData } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    // State management\n    const [cuestionariosData, setCuestionariosData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCuestionario, setSelectedCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCuestionarioId, setSelectedCuestionarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailView, setShowDetailView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCuestionario, setEditingCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cuestionarios, setCuestionarios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jsonContent, setJsonContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Fetch cuestionarios when component mounts\n    const fetchCuestionarios = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios\"));\n            if (!response.ok) {\n                // If it's a 404 or 500, just set empty array instead of showing error\n                if (response.status === 404 || response.status === 500) {\n                    setCuestionarios([]);\n                    return;\n                }\n                throw new Error('Error al cargar los cuestionarios');\n            }\n            const data = await response.json();\n            setCuestionarios(data || []);\n        } catch (err) {\n            console.error('Error fetching cuestionarios:', err);\n            // Don't show error to user, just set empty array\n            setCuestionarios([]);\n        }\n    };\n    // Load cuestionarios on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CuestionarioComponent.useEffect\": ()=>{\n            fetchCuestionarios();\n        }\n    }[\"CuestionarioComponent.useEffect\"], []);\n    // New structure functions\n    const addCuestionario = ()=>{\n        const newCuestionario = {\n            id: Date.now().toString(),\n            pregunta: '',\n            respuesta_correcta: 'A',\n            explicacion_general: '',\n            opciones: [\n                {\n                    id: '1',\n                    opcion: 'A. ',\n                    es_correcta: true,\n                    explicacion: ''\n                },\n                {\n                    id: '2',\n                    opcion: 'B. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '3',\n                    opcion: 'C. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '4',\n                    opcion: 'D. ',\n                    es_correcta: false,\n                    explicacion: ''\n                }\n            ]\n        };\n        setCuestionariosData([\n            ...cuestionariosData,\n            newCuestionario\n        ]);\n    };\n    const removeCuestionario = (id)=>{\n        setCuestionariosData(cuestionariosData.filter((c)=>c.id !== id));\n    };\n    const updateCuestionario = (id, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === id ? {\n                ...c,\n                [field]: value\n            } : c));\n    };\n    const updateOpcion = (cuestionarioId, opcionId, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                opciones: c.opciones.map((o)=>o.id === opcionId ? {\n                        ...o,\n                        [field]: value\n                    } : o)\n            } : c));\n    };\n    const setCorrectAnswer = (cuestionarioId, respuestaCorrecta)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                respuesta_correcta: respuestaCorrecta,\n                opciones: c.opciones.map((o)=>({\n                        ...o,\n                        es_correcta: o.opcion.startsWith(respuestaCorrecta + '.')\n                    }))\n            } : c));\n    };\n    // CRUD functions for existing cuestionarios\n    const updateCuestionarioInDB = async (cuestionarioId, updates)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                throw new Error('Error al actualizar el cuestionario');\n            }\n            const updatedCuestionario = await response.json();\n            // Update local state\n            setCuestionarios(cuestionarios.map((c)=>c.id === cuestionarioId ? updatedCuestionario : c));\n            return updatedCuestionario;\n        } catch (err) {\n            console.error('Error updating cuestionario:', err);\n            setError('Error al actualizar el cuestionario');\n            throw err;\n        }\n    };\n    const deleteCuestionarioFromDB = async (cuestionarioId)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Error al eliminar el cuestionario');\n            }\n            // Update local state\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            if (selectedCuestionarioId === cuestionarioId) {\n                setSelectedCuestionario(null);\n                setSelectedCuestionarioId(null);\n                setShowDetailView(false);\n            }\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Image upload functions\n    const uploadImage = async (file, type, cuestionarioId, opcionId)=>{\n        const uploadId = \"\".concat(cuestionarioId, \"-\").concat(type, \"-\").concat(opcionId || 'pregunta');\n        setUploadingImages((prev)=>new Set([\n                ...prev,\n                uploadId\n            ]));\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('content_type', 'cuestionarios');\n            formData.append('content_id', cuestionarioId.toString());\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/upload-image\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error('Error al subir la imagen');\n            }\n            const result = await response.json();\n            // Update the cuestionario with the new image URL\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = result.url;\n                    pregunta.imagen_path = result.path;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = result.url;\n                        opcion.imagen_path = result.path;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n            return result;\n        } catch (err) {\n            console.error('Error uploading image:', err);\n            setError('Error al subir la imagen');\n            throw err;\n        } finally{\n            setUploadingImages((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(uploadId);\n                return newSet;\n            });\n        }\n    };\n    const removeImage = async (cuestionarioId, type, opcionId)=>{\n        try {\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = undefined;\n                    pregunta.imagen_path = undefined;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = undefined;\n                        opcion.imagen_path = undefined;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n        } catch (err) {\n            console.error('Error removing image:', err);\n            setError('Error al eliminar la imagen');\n        }\n    };\n    const handleDelete = async (cuestionarioId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar este cuestionario?')) return;\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar el cuestionario');\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Handle JSON content submission\n    const handleJsonSubmit = async ()=>{\n        if (!jsonContent.trim()) {\n            setError('Por favor ingrese el contenido JSON');\n            return;\n        }\n        if (!sharedData.especialidad || !sharedData.tema) {\n            setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            // Validate JSON format first\n            let parsedJson;\n            try {\n                parsedJson = JSON.parse(jsonContent);\n            } catch (parseError) {\n                throw new Error('Formato JSON inválido');\n            }\n            // Validate structure\n            if (!Array.isArray(parsedJson) || parsedJson.length === 0) {\n                throw new Error('El JSON debe contener una lista de cuestionarios');\n            }\n            // Validate required fields\n            const requiredFields = [\n                'id',\n                'pregunta',\n                'opciones',\n                'respuesta_correcta',\n                'explicacion_general'\n            ];\n            for (const cuestionario of parsedJson){\n                for (const field of requiredFields){\n                    if (!(field in cuestionario)) {\n                        throw new Error(\"Campo requerido faltante: \".concat(field));\n                    }\n                }\n            }\n            const formData = new FormData();\n            formData.append('json_content', jsonContent);\n            formData.append('especialidad', sharedData.especialidad);\n            formData.append('sistema', 'TuResiBo');\n            formData.append('tema', sharedData.tema);\n            if (sharedData.titulo) {\n                formData.append('titulo', sharedData.titulo);\n            }\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/json\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al procesar el contenido JSON');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                setError(null);\n                setJsonContent('');\n                fetchCuestionarios();\n            } else {\n                throw new Error(result.message || 'Error al procesar el contenido JSON');\n            }\n        } catch (err) {\n            console.error('Error processing JSON:', err);\n            setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // Enhanced detailed view component with inline editing\n    const DetailedView = (param)=>{\n        let { cuestionario, cuestionarioId, isEditing, onToggleEdit } = param;\n        _s1();\n        const [localCuestionario, setLocalCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(cuestionario);\n        const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const handleSave = async ()=>{\n            setIsSaving(true);\n            try {\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: [\n                        localCuestionario\n                    ]\n                });\n                onToggleEdit();\n            } catch (err) {\n                console.error('Error saving:', err);\n            } finally{\n                setIsSaving(false);\n            }\n        };\n        const handleImageUpload = async (file, type, opcionId)=>{\n            try {\n                await uploadImage(file, type, cuestionarioId, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error uploading image:', err);\n            }\n        };\n        const handleImageRemove = async (type, opcionId)=>{\n            try {\n                await removeImage(cuestionarioId, type, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error removing image:', err);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Cuestionario Detallado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        disabled: isSaving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: isSaving ? 'Guardando...' : 'Guardar'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: onToggleEdit,\n                                        variant: \"outline\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onToggleEdit,\n                                variant: \"outline\",\n                                children: \"Editar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-3\",\n                            children: \"Pregunta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.pregunta,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    pregunta: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed mb-4\",\n                            children: cuestionario.pregunta\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: cuestionario.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: cuestionario.imagen_url,\n                                        alt: \"Imagen de la pregunta\",\n                                        className: \"max-w-full h-auto rounded-lg border\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>handleImageRemove('pregunta'),\n                                        variant: \"destructive\",\n                                        size: \"sm\",\n                                        className: \"absolute top-2 right-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Agregar imagen a la pregunta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"file\",\n                                        accept: \"image/*\",\n                                        onChange: (e)=>{\n                                            var _e_target_files;\n                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                            if (file) handleImageUpload(file, 'pregunta');\n                                        },\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Opciones de Respuesta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        (isEditing ? localCuestionario.opciones : cuestionario.opciones).map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4 \".concat(opcion.es_correcta ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-white'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium \".concat(opcion.es_correcta ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-700'),\n                                            children: String.fromCharCode(65 + index)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-3\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    value: opcion.opcion,\n                                                    onChange: (e)=>{\n                                                        const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                ...o,\n                                                                opcion: e.target.value\n                                                            } : o);\n                                                        setLocalCuestionario({\n                                                            ...localCuestionario,\n                                                            opciones: updatedOpciones\n                                                        });\n                                                    },\n                                                    className: \"font-medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: opcion.opcion\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 border border-gray-200 rounded p-3\",\n                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                        value: opcion.explicacion,\n                                                        onChange: (e)=>{\n                                                            const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                    ...o,\n                                                                    explicacion: e.target.value\n                                                                } : o);\n                                                            setLocalCuestionario({\n                                                                ...localCuestionario,\n                                                                opciones: updatedOpciones\n                                                            });\n                                                        },\n                                                        rows: 2,\n                                                        className: \"text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Explicaci\\xf3n:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            opcion.explicacion\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: opcion.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: opcion.imagen_url,\n                                                                alt: \"Imagen opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                className: \"max-w-full h-auto rounded border\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: ()=>handleImageRemove('opcion', opcion.id),\n                                                                variant: \"destructive\",\n                                                                size: \"sm\",\n                                                                className: \"absolute top-2 right-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 23\n                                                    }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Agregar imagen a la opci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleImageUpload(file, 'opcion', opcion.id);\n                                                                },\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"correct-answer-\".concat(cuestionarioId),\n                                                            checked: opcion.es_correcta,\n                                                            onChange: ()=>{\n                                                                const letter = String.fromCharCode(65 + index);\n                                                                const updatedOpciones = localCuestionario.opciones.map((o)=>({\n                                                                        ...o,\n                                                                        es_correcta: o.id === opcion.id\n                                                                    }));\n                                                                setLocalCuestionario({\n                                                                    ...localCuestionario,\n                                                                    opciones: updatedOpciones,\n                                                                    respuesta_correcta: letter\n                                                                });\n                                                            },\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm\",\n                                                            children: \"Respuesta correcta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"\".concat(cuestionarioId, \"-opcion-\").concat(opcion.id, \"-\").concat(index), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-green-900 mb-2\",\n                            children: \"Respuesta Correcta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800\",\n                            children: [\n                                \"Opci\\xf3n \",\n                                cuestionario.respuesta_correcta\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-yellow-900 mb-3\",\n                            children: \"Explicaci\\xf3n General\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.explicacion_general,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    explicacion_general: e.target.value\n                                }),\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed\",\n                            children: cuestionario.explicacion_general\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 441,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(DetailedView, \"M9DyeJGnuD9oeGWL64ONYbhHnEY=\");\n    // Main component render\n    if (showDetailView && selectedCuestionario && selectedCuestionarioId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>{\n                        setShowDetailView(false);\n                        setSelectedCuestionario(null);\n                        setSelectedCuestionarioId(null);\n                        setEditingCuestionario(null);\n                    },\n                    variant: \"outline\",\n                    className: \"mb-4\",\n                    children: \"← Volver a la lista\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedView, {\n                    cuestionario: selectedCuestionario,\n                    cuestionarioId: selectedCuestionarioId,\n                    isEditing: editingCuestionario === selectedCuestionarioId,\n                    onToggleEdit: ()=>{\n                        if (editingCuestionario === selectedCuestionarioId) {\n                            setEditingCuestionario(null);\n                        } else {\n                            setEditingCuestionario(selectedCuestionarioId);\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Nuevo Cuestionario\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-blue-900\",\n                                                    children: \"Crear desde JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mb-3\",\n                                            children: \"Pega el contenido JSON para crear m\\xfaltiples cuestionarios de una vez.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"json-content\",\n                                                    placeholder: \"Pega aqu\\xed el contenido JSON...\",\n                                                    value: jsonContent,\n                                                    onChange: (e)=>setJsonContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"font-mono text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: handleJsonSubmit,\n                                                    disabled: !jsonContent.trim() || isUploading,\n                                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUploading ? 'Procesando...' : 'Crear desde JSON'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            className: \"mt-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    className: \"text-sm text-blue-700 cursor-pointer hover:text-blue-800\",\n                                                    children: \"Ver formato JSON requerido\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        children: '[\\n  {\\n    \"id\": \"1\",\\n    \"pregunta\": \"\\xbfCu\\xe1l es el tratamiento de primera l\\xednea para la hipertensi\\xf3n arterial?\",\\n    \"opciones\": [\\n      {\\n        \"opcion\": \"A. Inhibidores de la ECA (IECA)\",\\n        \"es_correcta\": true,\\n        \"explicacion\": \"Los IECA son considerados tratamiento de primera l\\xednea...\"\\n      },\\n      {\\n        \"opcion\": \"B. Betabloqueadores\",\\n        \"es_correcta\": false,\\n        \"explicacion\": \"Los betabloqueadores no son la primera opci\\xf3n...\"\\n      }\\n    ],\\n    \"respuesta_correcta\": \"A\",\\n    \"explicacion_general\": \"El manejo inicial de la hipertensi\\xf3n sigue las gu\\xedas...\"\\n  }\\n]'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-full border-t\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs uppercase\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-white px-2 text-gray-500\",\n                                                children: \"O crear manualmente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 border border-green-200 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-green-900\",\n                                                    children: \"Crear Cuestionario\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Cuestionarios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: addCuestionario,\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Agregar Cuestionario\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 17\n                                                }, this),\n                                                cuestionariosData.map((cuestionario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        className: \"p-4 bg-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"Cuestionario \",\n                                                                                cuestionario.id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeCuestionario(cuestionario.id),\n                                                                            className: \"text-red-600 hover:text-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Pregunta\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 822,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Escriba la pregunta...\",\n                                                                            value: cuestionario.pregunta,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'pregunta', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Opciones\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: cuestionario.opciones.map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"border rounded p-3 space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-sm\",\n                                                                                                    children: [\n                                                                                                        String.fromCharCode(65 + index),\n                                                                                                        \".\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 838,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    placeholder: \"Opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                                                    value: opcion.opcion,\n                                                                                                    onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'opcion', e.target.value),\n                                                                                                    className: \"flex-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 841,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"radio\",\n                                                                                                    name: \"correct-\".concat(cuestionario.id),\n                                                                                                    checked: opcion.es_correcta,\n                                                                                                    onChange: ()=>setCorrectAnswer(cuestionario.id, String.fromCharCode(65 + index)),\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 847,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 837,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                            placeholder: \"Explicaci\\xf3n de esta opci\\xf3n...\",\n                                                                                            value: opcion.explicacion,\n                                                                                            onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'explicacion', e.target.value),\n                                                                                            rows: 2,\n                                                                                            className: \"text-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 855,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, \"manual-\".concat(cuestionario.id, \"-opcion-\").concat(opcion.id, \"-\").concat(index), true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                    lineNumber: 836,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Explicaci\\xf3n General\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 869,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Explicaci\\xf3n general del cuestionario...\",\n                                                                            value: cuestionario.explicacion_general,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'explicacion_general', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 870,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, cuestionario.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                cuestionariosData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        // Convert to JSON and submit\n                                                        const jsonData = JSON.stringify(cuestionariosData, null, 2);\n                                                        setJsonContent(jsonData);\n                                                        handleJsonSubmit();\n                                                    },\n                                                    disabled: isUploading,\n                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                    children: isUploading ? 'Creando...' : 'Crear Cuestionarios'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    variant: \"destructive\",\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    className: \"border-green-200 bg-green-50 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        className: \"text-green-800\",\n                                        children: \"\\xa1Cuestionarios creados exitosamente!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 705,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"text-lg\",\n                                children: [\n                                    \"Cuestionarios (\",\n                                    cuestionarios.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: cuestionarios.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"No hay cuestionarios a\\xfan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 17\n                                }, this) : cuestionarios.map((cuestionario)=>{\n                                    var _cuestionario_preguntas;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group border rounded-lg p-3 hover:bg-gray-50 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-sm truncate\",\n                                                            children: cuestionario.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: cuestionario.especialidad\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        ((_cuestionario_preguntas = cuestionario.preguntas) === null || _cuestionario_preguntas === void 0 ? void 0 : _cuestionario_preguntas.length) || 0,\n                                                                        \" preguntas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1 truncate\",\n                                                            children: cuestionario.tema\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>{\n                                                                if (cuestionario.preguntas.length > 0) {\n                                                                    setSelectedCuestionario(cuestionario.preguntas[0]);\n                                                                    setSelectedCuestionarioId(cuestionario.id);\n                                                                    setShowDetailView(true);\n                                                                }\n                                                            },\n                                                            className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleDelete(cuestionario.id),\n                                                            className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, cuestionario.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 918,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n        lineNumber: 703,\n        columnNumber: 5\n    }, this);\n}\n_s(CuestionarioComponent, \"DEzfLj3BJjkqzgGuC+DTDXNpRLw=\");\n_c = CuestionarioComponent;\nvar _c;\n$RefreshReg$(_c, \"CuestionarioComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/cuestionario.tsx\n"));

/***/ })

});