package dto

import (
	"time"
)

// Base DTOs for common fields
type ContentBase struct {
	Title        string  `json:"title" binding:"required"`
	Especialidad string  `json:"especialidad" binding:"required"`
	Sistema      string  `json:"sistema" binding:"required"`
	Tema         string  `json:"tema" binding:"required"`
	Description  *string `json:"description,omitempty"`
}

// Videoclase DTOs
type VideoclaseCreate struct {
	ContentBase
	URL          *string `json:"url,omitempty"`
	FilePath     *string `json:"file_path,omitempty"`
	ThumbnailURL *string `json:"thumbnail_url,omitempty"`
	Duration     *int    `json:"duration,omitempty"`
}

type VideoclaseUpdate struct {
	Title        *string `json:"title,omitempty"`
	Especialidad *string `json:"especialidad,omitempty"`
	Sistema      *string `json:"sistema,omitempty"`
	Tema         *string `json:"tema,omitempty"`
	URL          *string `json:"url,omitempty"`
	FilePath     *string `json:"file_path,omitempty"`
	ThumbnailURL *string `json:"thumbnail_url,omitempty"`
	Duration     *int    `json:"duration,omitempty"`
	Description  *string `json:"description,omitempty"`
}

type VideoclaseOut struct {
	ID           uint      `json:"id"`
	Title        string    `json:"title"`
	Especialidad string    `json:"especialidad"`
	Sistema      string    `json:"sistema"`
	Tema         string    `json:"tema"`
	URL          *string   `json:"url,omitempty"`
	FilePath     *string   `json:"file_path,omitempty"`
	ThumbnailURL *string   `json:"thumbnail_url,omitempty"`
	Duration     *int      `json:"duration,omitempty"`
	Description  *string   `json:"description,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
}

// Video Corto DTOs
type VideoCortoCreate struct {
	ContentBase
	URL          *string `json:"url,omitempty"`
	FilePath     *string `json:"file_path,omitempty"`
	ThumbnailURL *string `json:"thumbnail_url,omitempty"`
	Duration     *int    `json:"duration,omitempty"`
}

type VideoCortoUpdate struct {
	Title        *string `json:"title,omitempty"`
	Especialidad *string `json:"especialidad,omitempty"`
	Sistema      *string `json:"sistema,omitempty"`
	Tema         *string `json:"tema,omitempty"`
	URL          *string `json:"url,omitempty"`
	FilePath     *string `json:"file_path,omitempty"`
	ThumbnailURL *string `json:"thumbnail_url,omitempty"`
	Duration     *int    `json:"duration,omitempty"`
	Description  *string `json:"description,omitempty"`
}

type VideoCortoOut struct {
	ID           uint      `json:"id"`
	Title        string    `json:"title"`
	Especialidad string    `json:"especialidad"`
	Sistema      string    `json:"sistema"`
	Tema         string    `json:"tema"`
	URL          *string   `json:"url,omitempty"`
	FilePath     *string   `json:"file_path,omitempty"`
	ThumbnailURL *string   `json:"thumbnail_url,omitempty"`
	Duration     *int      `json:"duration,omitempty"`
	Description  *string   `json:"description,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
}

// Nota Clinica DTOs
type NotaClinicaCreate struct {
	ContentBase
	Content string                   `json:"content" binding:"required"`
	Images  []map[string]interface{} `json:"images,omitempty"`
}

type NotaClinicaUpdate struct {
	Title        *string                   `json:"title,omitempty"`
	Especialidad *string                   `json:"especialidad,omitempty"`
	Sistema      *string                   `json:"sistema,omitempty"`
	Tema         *string                   `json:"tema,omitempty"`
	Content      *string                   `json:"content,omitempty"`
	Images       *[]map[string]interface{} `json:"images,omitempty"`
}

type NotaClinicaOut struct {
	ID           uint                     `json:"id"`
	Title        string                   `json:"title"`
	Especialidad string                   `json:"especialidad"`
	Sistema      string                   `json:"sistema"`
	Tema         string                   `json:"tema"`
	Content      string                   `json:"content"`
	Images       []map[string]interface{} `json:"images,omitempty"`
	CreatedAt    time.Time                `json:"created_at"`
}

// Caso Clinico DTOs
type CasoClinicoCreate struct {
	ContentBase
	Descripcion string                   `json:"descripcion" binding:"required"`
	Images      []map[string]interface{} `json:"images,omitempty"`
	Preguntas   []map[string]interface{} `json:"preguntas" binding:"required"`
}

type CasoClinicoUpdate struct {
	Title        *string                   `json:"title,omitempty"`
	Especialidad *string                   `json:"especialidad,omitempty"`
	Sistema      *string                   `json:"sistema,omitempty"`
	Tema         *string                   `json:"tema,omitempty"`
	Descripcion  *string                   `json:"descripcion,omitempty"`
	Images       *[]map[string]interface{} `json:"images,omitempty"`
	Preguntas    *[]map[string]interface{} `json:"preguntas,omitempty"`
}

type CasoClinicoOut struct {
	ID           uint                     `json:"id"`
	Title        string                   `json:"title"`
	Especialidad string                   `json:"especialidad"`
	Sistema      string                   `json:"sistema"`
	Tema         string                   `json:"tema"`
	Descripcion  string                   `json:"descripcion"`
	Images       []map[string]interface{} `json:"images,omitempty"`
	Preguntas    []map[string]interface{} `json:"preguntas"`
	CreatedAt    time.Time                `json:"created_at"`
}

// Cuestionario DTOs
type CuestionarioCreate struct {
	ContentBase
	Preguntas []map[string]interface{} `json:"preguntas" binding:"required"`
}

type CuestionarioUpdate struct {
	Title        *string                   `json:"title,omitempty"`
	Especialidad *string                   `json:"especialidad,omitempty"`
	Sistema      *string                   `json:"sistema,omitempty"`
	Tema         *string                   `json:"tema,omitempty"`
	Preguntas    *[]map[string]interface{} `json:"preguntas,omitempty"`
}

type CuestionarioOut struct {
	ID           uint                     `json:"id"`
	Title        string                   `json:"title"`
	Especialidad string                   `json:"especialidad"`
	Sistema      string                   `json:"sistema"`
	Tema         string                   `json:"tema"`
	Preguntas    []map[string]interface{} `json:"preguntas"`
	CreatedAt    time.Time                `json:"created_at"`
}

// Flashcard DTOs
type FlashcardCreate struct {
	ContentBase
	Pregunta  string   `json:"pregunta" binding:"required"`
	Respuesta string   `json:"respuesta" binding:"required"`
	Etiquetas []string `json:"etiquetas,omitempty"`
}

type FlashcardUpdate struct {
	Title        *string   `json:"title,omitempty"`
	Especialidad *string   `json:"especialidad,omitempty"`
	Sistema      *string   `json:"sistema,omitempty"`
	Tema         *string   `json:"tema,omitempty"`
	Pregunta     *string   `json:"pregunta,omitempty"`
	Respuesta    *string   `json:"respuesta,omitempty"`
	Etiquetas    *[]string `json:"etiquetas,omitempty"`
}

type FlashcardOut struct {
	ID           uint      `json:"id"`
	Title        string    `json:"title"`
	Especialidad string    `json:"especialidad"`
	Sistema      string    `json:"sistema"`
	Tema         string    `json:"tema"`
	Pregunta     string    `json:"pregunta"`
	Respuesta    string    `json:"respuesta"`
	Etiquetas    []string  `json:"etiquetas,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
}

// Repaso DTOs
type RepasoCreate struct {
	ContentBase
	ImageURL *string `json:"image_url,omitempty"`
	FilePath *string `json:"file_path,omitempty"`
}

type RepasoUpdate struct {
	Title        *string `json:"title,omitempty"`
	Especialidad *string `json:"especialidad,omitempty"`
	Sistema      *string `json:"sistema,omitempty"`
	Tema         *string `json:"tema,omitempty"`
	ImageURL     *string `json:"image_url,omitempty"`
	FilePath     *string `json:"file_path,omitempty"`
	Description  *string `json:"description,omitempty"`
}

type RepasoOut struct {
	ID           uint      `json:"id"`
	Title        string    `json:"title"`
	Especialidad string    `json:"especialidad"`
	Sistema      string    `json:"sistema"`
	Tema         string    `json:"tema"`
	ImageURL     *string   `json:"image_url,omitempty"`
	FilePath     *string   `json:"file_path,omitempty"`
	Description  *string   `json:"description,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
}

// JSON Upload DTOs for Cuestionarios
type OpcionCuestionarioJsonCreate struct {
	Opcion      string `json:"opcion" binding:"required"`
	EsCorrecta  bool   `json:"es_correcta"`
	Explicacion string `json:"explicacion" binding:"required"`
}

type CuestionarioJsonCreate struct {
	ID                 string                         `json:"id" binding:"required"`
	Pregunta           string                         `json:"pregunta" binding:"required"`
	Opciones           []OpcionCuestionarioJsonCreate `json:"opciones" binding:"required"`
	RespuestaCorrecta  string                         `json:"respuesta_correcta" binding:"required"`
	ExplicacionGeneral string                         `json:"explicacion_general" binding:"required"`
}

type CuestionarioJsonUpload struct {
	Cuestionarios []CuestionarioJsonCreate `json:"cuestionarios" binding:"required"`
	Especialidad  string                   `json:"especialidad" binding:"required"`
	Sistema       string                   `json:"sistema" binding:"required"`
	Tema          string                   `json:"tema" binding:"required"`
	Title         *string                  `json:"title,omitempty"`
}

// Response DTOs
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}
