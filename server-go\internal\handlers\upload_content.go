package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/middleware"
	"turesibo-server/internal/repository"
	"turesibo-server/internal/services"
)

// UploadHandler handles upload content operations
type UploadHandler struct {
	repo       *repository.UploadContentRepository
	gcsService *services.GCSService
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(db *gorm.DB, gcsService *services.GCSService) *UploadHandler {
	var repo *repository.UploadContentRepository
	if db != nil {
		repo = repository.NewUploadContentRepository(db)
	}

	return &UploadHandler{
		repo:       repo,
		gcsService: gcsService,
	}
}

// RegisterRoutes registers all upload content routes
func (h *UploadHandler) RegisterRoutes(router *gin.RouterGroup) {
	// Videoclases routes
	router.POST("/upload-video", h.UploadVideo)
	router.GET("/videos", h.ListVideoclases)
	router.PUT("/videos/:id", h.UpdateVideoclase)
	router.DELETE("/videos/:id", h.DeleteVideoclase)

	// Videos cortos routes
	router.POST("/upload-video-corto", h.UploadVideoCorto)
	router.GET("/videos-cortos", h.ListVideosCortos)
	router.PUT("/videos-cortos/:id", h.UpdateVideoCorto)
	router.DELETE("/videos-cortos/:id", h.DeleteVideoCorto)

	// Clinical notes routes
	router.POST("/clinical-notes", h.UploadClinicalNotes)
	router.GET("/clinical-notes", h.ListClinicalNotes)
	router.GET("/clinical-notes/:id", h.GetClinicalNote)

	// Clinical cases routes
	router.POST("/clinical-cases", h.UploadClinicalCase)
	router.GET("/clinical-cases", h.ListClinicalCases)
	router.GET("/clinical-cases/:id", h.GetClinicalCase)
	router.POST("/clinical-cases/json", h.UploadClinicalCasesJSON)
	router.PUT("/clinical-cases/:id", h.UpdateClinicalCase)
	router.DELETE("/clinical-cases/:id", h.DeleteClinicalCase)

	// Cuestionarios routes
	router.POST("/cuestionarios", h.CreateCuestionario)
	router.GET("/cuestionarios", h.ListCuestionarios)
	router.GET("/cuestionarios/:id", h.GetCuestionario)
	router.POST("/cuestionarios/json", h.UploadCuestionariosJSON)
	router.PUT("/cuestionarios/:id", h.UpdateCuestionario)
	router.DELETE("/cuestionarios/:id", h.DeleteCuestionario)

	// Flashcards routes
	router.POST("/flashcards", h.CreateFlashcard)
	router.GET("/flashcards", h.ListFlashcards)
	router.GET("/flashcards/:id", h.GetFlashcard)
	router.POST("/flashcards/json", h.UploadFlashcardsJSON)
	router.PUT("/flashcards/:id", h.UpdateFlashcard)
	router.DELETE("/flashcards/:id", h.DeleteFlashcard)

	// Utility routes
	router.GET("/especialidades", h.GetEspecialidades)
	router.GET("/especialidades/:especialidad/sistemas", h.GetSistemasByEspecialidad)
	router.GET("/especialidades/:especialidad/sistemas/:sistema/temas", h.GetTemasByEspecialidadSistema)
}

// RegisterContentRoutes registers content display routes (for frontend consumption)
func (h *UploadHandler) RegisterContentRoutes(router *gin.RouterGroup) {
	// Content display routes with filtering by especialidad, sistema, tema
	router.GET("/videoclases/", h.GetVideoclasesByFilter)
	router.GET("/videos-cortos/", h.GetVideosCortosbyFilter)
	router.GET("/notas-clinicas/", h.GetNotasClinicasByFilter)
	router.GET("/casos-clinicos/", h.GetCasosClinicosByFilter)
	router.GET("/cuestionarios/", h.GetCuestionariosByFilter)
	router.GET("/flashcards/", h.GetFlashcardsByFilter)
	router.GET("/repaso/", h.GetRepasoByFilter)

	// Utility routes for content
	router.GET("/especialidades/", h.GetEspecialidades)
	router.GET("/sistemas/", h.GetSistemasByEspecialidadQuery)
	router.GET("/temas/", h.GetTemasByEspecialidadSistemaQuery)
}

// Helper function to validate file extensions
func (h *UploadHandler) validateVideoFile(filename string) bool {
	allowedExtensions := map[string]bool{
		".mp4":  true,
		".webm": true,
		".mov":  true,
		".avi":  true,
		".mkv":  true,
	}
	ext := strings.ToLower(filepath.Ext(filename))
	return allowedExtensions[ext]
}

func (h *UploadHandler) validateImageFile(filename string) bool {
	allowedExtensions := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".webp": true,
		".gif":  true,
	}
	ext := strings.ToLower(filepath.Ext(filename))
	return allowedExtensions[ext]
}

// UploadVideo handles video upload for videoclases
func (h *UploadHandler) UploadVideo(c *gin.Context) {
	ctx := context.Background()

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")
	quiz := c.PostForm("quiz")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	var videoURL, videoFilePath, thumbnailURL *string

	// Handle video file upload
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Validate video file
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videoclases/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		videoURL = &url
		videoFilePath = &filePath
	}

	// Handle thumbnail upload
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate thumbnail file
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		thumbnailURL = &url
	}

	// Create videoclase if video was uploaded
	var videoclase *dto.VideoclaseOut
	if videoURL != nil {
		createData := &dto.VideoclaseCreate{
			ContentBase: dto.ContentBase{
				Title:        titulo,
				Especialidad: especialidad,
				Sistema:      sistema,
				Tema:         tema,
			},
			URL:          videoURL,
			FilePath:     videoFilePath,
			ThumbnailURL: thumbnailURL,
		}

		createdVideoclase, err := h.repo.CreateVideoclase(createData)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to create videoclase", err)
			return
		}

		videoclase = &dto.VideoclaseOut{
			ID:           createdVideoclase.ID,
			Title:        createdVideoclase.Title,
			Especialidad: createdVideoclase.Especialidad,
			Sistema:      createdVideoclase.Sistema,
			Tema:         createdVideoclase.Tema,
			URL:          createdVideoclase.URL,
			FilePath:     createdVideoclase.FilePath,
			ThumbnailURL: createdVideoclase.ThumbnailURL,
			Duration:     createdVideoclase.Duration,
			Description:  createdVideoclase.Description,
			CreatedAt:    createdVideoclase.CreatedAt,
		}
	}

	// Handle quiz creation if provided
	if quiz != "" {
		var quizData map[string]interface{}
		if err := json.Unmarshal([]byte(quiz), &quizData); err != nil {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid quiz JSON format", err)
			return
		}

		// Convert quiz data to cuestionario format
		preguntas, ok := quizData["preguntas"].([]interface{})
		if !ok {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid quiz format: missing preguntas", nil)
			return
		}

		preguntasData := make([]map[string]interface{}, len(preguntas))
		for i, p := range preguntas {
			pregunta, ok := p.(map[string]interface{})
			if !ok {
				middleware.HandleError(c, http.StatusBadRequest, "Invalid question format", nil)
				return
			}
			preguntasData[i] = pregunta
		}

		cuestionarioData := &dto.CuestionarioCreate{
			ContentBase: dto.ContentBase{
				Title:        fmt.Sprintf("Cuestionario - %s", titulo),
				Especialidad: especialidad,
				Sistema:      sistema,
				Tema:         tema,
			},
			Preguntas: preguntasData,
		}

		_, err := h.repo.CreateCuestionario(cuestionarioData)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to create quiz", err)
			return
		}
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Videoclase subida exitosamente",
	}

	if videoclase != nil {
		response["videoclase"] = videoclase
	}

	c.JSON(http.StatusOK, response)
}

// ListVideoclases handles listing all videoclases
func (h *UploadHandler) ListVideoclases(c *gin.Context) {
	videoclases, err := h.repo.ListVideoclases()
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to list videoclases", err)
		return
	}

	// Convert to DTOs
	videoclasesOut := make([]dto.VideoclaseOut, len(videoclases))
	for i, v := range videoclases {
		videoclasesOut[i] = dto.VideoclaseOut{
			ID:           v.ID,
			Title:        v.Title,
			Especialidad: v.Especialidad,
			Sistema:      v.Sistema,
			Tema:         v.Tema,
			URL:          v.URL,
			FilePath:     v.FilePath,
			ThumbnailURL: v.ThumbnailURL,
			Duration:     v.Duration,
			Description:  v.Description,
			CreatedAt:    v.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, videoclasesOut)
}

// UpdateVideoclase handles updating a videoclase
func (h *UploadHandler) UpdateVideoclase(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing videoclase
	existingVideoclase, err := h.repo.GetVideoclase(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Videoclase not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get videoclase", err)
		}
		return
	}

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	updateData := &dto.VideoclaseUpdate{
		Title:        &titulo,
		Especialidad: &especialidad,
		Sistema:      &sistema,
		Tema:         &tema,
		URL:          existingVideoclase.URL,
		FilePath:     existingVideoclase.FilePath,
		ThumbnailURL: existingVideoclase.ThumbnailURL,
	}

	// Handle video file update
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Delete old video file if exists
		if existingVideoclase.FilePath != nil {
			if err := h.gcsService.DeleteFile(ctx, *existingVideoclase.FilePath); err != nil {
				// Log error but don't fail the request
				fmt.Printf("Error deleting old video file: %v\n", err)
			}
		}

		// Validate and upload new video
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videoclases/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		updateData.URL = &url
		updateData.FilePath = &filePath
	}

	// Handle thumbnail update
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate and upload new thumbnail
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		updateData.ThumbnailURL = &url
	}

	// Update videoclase
	updatedVideoclase, err := h.repo.UpdateVideoclase(uint(id), updateData)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to update videoclase", err)
		return
	}

	videoclaseOut := dto.VideoclaseOut{
		ID:           updatedVideoclase.ID,
		Title:        updatedVideoclase.Title,
		Especialidad: updatedVideoclase.Especialidad,
		Sistema:      updatedVideoclase.Sistema,
		Tema:         updatedVideoclase.Tema,
		URL:          updatedVideoclase.URL,
		FilePath:     updatedVideoclase.FilePath,
		ThumbnailURL: updatedVideoclase.ThumbnailURL,
		Duration:     updatedVideoclase.Duration,
		Description:  updatedVideoclase.Description,
		CreatedAt:    updatedVideoclase.CreatedAt,
	}

	response := map[string]interface{}{
		"success":    true,
		"message":    "Videoclase actualizada exitosamente",
		"videoclase": videoclaseOut,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteVideoclase handles deleting a videoclase
func (h *UploadHandler) DeleteVideoclase(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing videoclase to delete associated files
	existingVideoclase, err := h.repo.GetVideoclase(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Videoclase not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get videoclase", err)
		}
		return
	}

	// Delete associated files from GCS
	if existingVideoclase.FilePath != nil {
		if err := h.gcsService.DeleteFile(ctx, *existingVideoclase.FilePath); err != nil {
			// Log error but don't fail the request
			fmt.Printf("Error deleting video file: %v\n", err)
		}
	}

	// Delete videoclase from database
	if err := h.repo.DeleteVideoclase(uint(id)); err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to delete videoclase", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Videoclase eliminada exitosamente",
	}

	c.JSON(http.StatusOK, response)
}

// UploadClinicalCasesJSON handles uploading clinical cases from JSON content
func (h *UploadHandler) UploadClinicalCasesJSON(c *gin.Context) {
	// Parse form data
	jsonContent := c.PostForm("json_content")
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	_ = c.PostForm("titulo") // titulo is optional, not used in this implementation

	if jsonContent == "" || especialidad == "" || sistema == "" || tema == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	// Parse JSON content
	var jsonData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &jsonData); err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid JSON format", err)
		return
	}

	// Validate JSON structure
	if len(jsonData) == 0 {
		middleware.HandleError(c, http.StatusBadRequest, "JSON must contain a list of clinical cases", nil)
		return
	}

	// Validate required fields for each case
	for i, casoData := range jsonData {
		requiredFields := []string{"id", "caso_clinico", "pregunta", "opciones", "respuesta_correcta", "explicacion_general"}
		for _, field := range requiredFields {
			if _, exists := casoData[field]; !exists {
				middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Missing required field '%s' in case %d", field, i), nil)
				return
			}
		}

		// Validate opciones structure
		opciones, ok := casoData["opciones"].([]interface{})
		if !ok {
			middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Options must be a list in case %d", i), nil)
			return
		}

		for j, opcion := range opciones {
			opcionMap, ok := opcion.(map[string]interface{})
			if !ok {
				middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid option format in case %d, option %d", i, j), nil)
				return
			}

			requiredOpcionFields := []string{"opcion", "es_correcta", "explicacion"}
			for _, field := range requiredOpcionFields {
				if _, exists := opcionMap[field]; !exists {
					middleware.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Missing required field '%s' in case %d, option %d", field, i, j), nil)
					return
				}
			}
		}
	}

	// Check if repository is available
	if h.repo == nil {
		middleware.HandleError(c, http.StatusServiceUnavailable, "Database service not available", nil)
		return
	}

	// Create clinical cases using repository
	createdCasos, err := h.repo.ListCasosClinicosFromJSON(especialidad, sistema, tema, jsonData)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to create clinical cases", err)
		return
	}

	// Prepare response
	casosResponse := make([]map[string]interface{}, len(createdCasos))
	for i, caso := range createdCasos {
		casosResponse[i] = map[string]interface{}{
			"id":           caso.ID,
			"title":        caso.Title,
			"especialidad": caso.Especialidad,
			"tema":         caso.Tema,
			"created_at":   caso.CreatedAt,
		}
	}

	response := map[string]interface{}{
		"success":        true,
		"message":        fmt.Sprintf("Se crearon %d casos clínicos exitosamente", len(createdCasos)),
		"casos_creados":  len(createdCasos),
		"casos_clinicos": casosResponse,
	}

	c.JSON(http.StatusOK, response)
}

// ListClinicalCases handles listing all clinical cases
func (h *UploadHandler) ListClinicalCases(c *gin.Context) {
	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{}) // Return empty array when DB not available
		return
	}

	casosClinicosData, err := h.repo.ListCasosClinicosData()
	if err != nil {
		// Log the error but return empty array instead of error
		fmt.Printf("Warning: Failed to list clinical cases: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	casosOut := make([]dto.CasoClinicoOut, len(casosClinicosData))
	for i, caso := range casosClinicosData {
		// Parse images JSON
		var images []map[string]interface{}
		if len(caso.Images) > 0 {
			if err := json.Unmarshal(caso.Images, &images); err != nil {
				fmt.Printf("Warning: Failed to parse images for case %d: %v\n", caso.ID, err)
				images = []map[string]interface{}{}
			}
		}

		// Parse preguntas JSON
		var preguntas []map[string]interface{}
		if len(caso.Preguntas) > 0 {
			if err := json.Unmarshal(caso.Preguntas, &preguntas); err != nil {
				fmt.Printf("Warning: Failed to parse preguntas for case %d: %v\n", caso.ID, err)
				preguntas = []map[string]interface{}{}
			}
		}

		casosOut[i] = dto.CasoClinicoOut{
			ID:           caso.ID,
			Title:        caso.Title,
			Especialidad: caso.Especialidad,
			Sistema:      caso.Sistema,
			Tema:         caso.Tema,
			Descripcion:  caso.Descripcion,
			Images:       images,
			Preguntas:    preguntas,
			CreatedAt:    caso.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, casosOut)
}

// Content filtering handlers for frontend consumption

// GetVideoclasesByFilter handles getting videoclases with optional filters
func (h *UploadHandler) GetVideoclasesByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	videoclases, err := h.repo.GetVideoclasesByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get videoclases: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	videoclasesOut := make([]dto.VideoclaseOut, len(videoclases))
	for i, v := range videoclases {
		videoclasesOut[i] = dto.VideoclaseOut{
			ID:           v.ID,
			Title:        v.Title,
			Especialidad: v.Especialidad,
			Sistema:      v.Sistema,
			Tema:         v.Tema,
			URL:          v.URL,
			FilePath:     v.FilePath,
			ThumbnailURL: v.ThumbnailURL,
			Duration:     v.Duration,
			Description:  v.Description,
			CreatedAt:    v.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, videoclasesOut)
}

// GetVideosCortosbyFilter handles getting videos cortos with optional filters
func (h *UploadHandler) GetVideosCortosbyFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	videosCortos, err := h.repo.GetVideosCortosbyFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get videos cortos: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs (assuming similar structure to videoclases)
	videosOut := make([]interface{}, len(videosCortos))
	for i, v := range videosCortos {
		videosOut[i] = map[string]interface{}{
			"id":            v.ID,
			"title":         v.Title,
			"especialidad":  v.Especialidad,
			"sistema":       v.Sistema,
			"tema":          v.Tema,
			"url":           v.URL,
			"file_path":     v.FilePath,
			"thumbnail_url": v.ThumbnailURL,
			"duration":      v.Duration,
			"description":   v.Description,
			"created_at":    v.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, videosOut)
}

// GetNotasClinicasByFilter handles getting notas clinicas with optional filters
func (h *UploadHandler) GetNotasClinicasByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	notasClinicas, err := h.repo.GetNotasClinicasByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get notas clinicas: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	notasOut := make([]interface{}, len(notasClinicas))
	for i, nota := range notasClinicas {
		notasOut[i] = map[string]interface{}{
			"id":           nota.ID,
			"title":        nota.Title,
			"especialidad": nota.Especialidad,
			"sistema":      nota.Sistema,
			"tema":         nota.Tema,
			"content":      nota.Content,
			"created_at":   nota.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, notasOut)
}

// GetCasosClinicosByFilter handles getting casos clinicos with optional filters
func (h *UploadHandler) GetCasosClinicosByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	casosClinicosData, err := h.repo.GetCasosClinicosByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get casos clinicos: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	casosOut := make([]dto.CasoClinicoOut, len(casosClinicosData))
	for i, caso := range casosClinicosData {
		// Parse images JSON
		var images []map[string]interface{}
		if len(caso.Images) > 0 {
			if err := json.Unmarshal(caso.Images, &images); err != nil {
				fmt.Printf("Warning: Failed to parse images for case %d: %v\n", caso.ID, err)
				images = []map[string]interface{}{}
			}
		}

		// Parse preguntas JSON
		var preguntas []map[string]interface{}
		if len(caso.Preguntas) > 0 {
			if err := json.Unmarshal(caso.Preguntas, &preguntas); err != nil {
				fmt.Printf("Warning: Failed to parse preguntas for case %d: %v\n", caso.ID, err)
				preguntas = []map[string]interface{}{}
			}
		}

		casosOut[i] = dto.CasoClinicoOut{
			ID:           caso.ID,
			Title:        caso.Title,
			Especialidad: caso.Especialidad,
			Sistema:      caso.Sistema,
			Tema:         caso.Tema,
			Descripcion:  caso.Descripcion,
			Images:       images,
			Preguntas:    preguntas,
			CreatedAt:    caso.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, casosOut)
}

// GetCuestionariosByFilter handles getting cuestionarios with optional filters
func (h *UploadHandler) GetCuestionariosByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	cuestionarios, err := h.repo.GetCuestionariosByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get cuestionarios: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	cuestionariosOut := make([]interface{}, len(cuestionarios))
	for i, cuestionario := range cuestionarios {
		// Parse preguntas JSON
		var preguntas []map[string]interface{}
		if len(cuestionario.Preguntas) > 0 {
			if err := json.Unmarshal(cuestionario.Preguntas, &preguntas); err != nil {
				fmt.Printf("Warning: Failed to parse preguntas for cuestionario %d: %v\n", cuestionario.ID, err)
				preguntas = []map[string]interface{}{}
			}
		}

		cuestionariosOut[i] = map[string]interface{}{
			"id":           cuestionario.ID,
			"title":        cuestionario.Title,
			"especialidad": cuestionario.Especialidad,
			"sistema":      cuestionario.Sistema,
			"tema":         cuestionario.Tema,
			"preguntas":    preguntas,
			"created_at":   cuestionario.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, cuestionariosOut)
}

// GetFlashcardsByFilter handles getting flashcards with optional filters
func (h *UploadHandler) GetFlashcardsByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	flashcards, err := h.repo.GetFlashcardsByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get flashcards: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	flashcardsOut := make([]interface{}, len(flashcards))
	for i, flashcard := range flashcards {
		flashcardsOut[i] = map[string]interface{}{
			"id":           flashcard.ID,
			"title":        flashcard.Title,
			"especialidad": flashcard.Especialidad,
			"sistema":      flashcard.Sistema,
			"tema":         flashcard.Tema,
			"pregunta":     flashcard.Pregunta,
			"respuesta":    flashcard.Respuesta,
			"etiquetas":    flashcard.Etiquetas,
			"created_at":   flashcard.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, flashcardsOut)
}

// GetRepasoByFilter handles getting repaso content with optional filters
func (h *UploadHandler) GetRepasoByFilter(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")
	tema := c.Query("tema")

	// URL decode the parameters to handle special characters like accents
	if especialidad != "" {
		if decoded, err := url.QueryUnescape(especialidad); err == nil {
			especialidad = decoded
		}
	}
	if sistema != "" {
		if decoded, err := url.QueryUnescape(sistema); err == nil {
			sistema = decoded
		}
	}
	if tema != "" {
		if decoded, err := url.QueryUnescape(tema); err == nil {
			tema = decoded
		}
	}

	// Check if repository is available
	if h.repo == nil {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	repaso, err := h.repo.GetRepasoByFilter(especialidad, sistema, tema)
	if err != nil {
		fmt.Printf("Warning: Failed to get repaso: %v\n", err)
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	// Convert to DTOs
	repasoOut := make([]interface{}, len(repaso))
	for i, item := range repaso {
		repasoOut[i] = map[string]interface{}{
			"id":           item.ID,
			"title":        item.Title,
			"especialidad": item.Especialidad,
			"sistema":      item.Sistema,
			"tema":         item.Tema,
			"description":  item.Description,
			"image_url":    item.ImageURL,
			"file_path":    item.FilePath,
			"created_at":   item.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, repasoOut)
}

// GetSistemasByEspecialidadQuery handles getting sistemas for a specific especialidad via query parameter
func (h *UploadHandler) GetSistemasByEspecialidadQuery(c *gin.Context) {
	especialidad := c.Query("especialidad")
	if especialidad == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad parameter", nil)
		return
	}

	// URL decode the parameter to handle special characters like accents
	if decoded, err := url.QueryUnescape(especialidad); err == nil {
		especialidad = decoded
	}

	sistemas, err := h.repo.GetSistemasByEspecialidad(especialidad)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get sistemas", err)
		return
	}

	c.JSON(http.StatusOK, sistemas)
}

// GetTemasByEspecialidadSistemaQuery handles getting temas for a specific especialidad and sistema via query parameters
func (h *UploadHandler) GetTemasByEspecialidadSistemaQuery(c *gin.Context) {
	especialidad := c.Query("especialidad")
	sistema := c.Query("sistema")

	if especialidad == "" || sistema == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad or sistema parameter", nil)
		return
	}

	// URL decode the parameters to handle special characters like accents
	if decoded, err := url.QueryUnescape(especialidad); err == nil {
		especialidad = decoded
	}
	if decoded, err := url.QueryUnescape(sistema); err == nil {
		sistema = decoded
	}

	temas, err := h.repo.GetTemasByEspecialidadSistema(especialidad, sistema)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get temas", err)
		return
	}

	c.JSON(http.StatusOK, temas)
}
