"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/cuestionario.tsx":
/*!*************************************!*\
  !*** ./app/upload/cuestionario.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CuestionarioComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Helper function to get API base URL\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nfunction CuestionarioComponent(param) {\n    let { sharedData } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    // State management\n    const [cuestionariosData, setCuestionariosData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCuestionario, setSelectedCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCuestionarioId, setSelectedCuestionarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailView, setShowDetailView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCuestionario, setEditingCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cuestionarios, setCuestionarios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jsonContent, setJsonContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Fetch cuestionarios when component mounts\n    const fetchCuestionarios = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios\"));\n            if (!response.ok) {\n                // If it's a 404 or 500, just set empty array instead of showing error\n                if (response.status === 404 || response.status === 500) {\n                    setCuestionarios([]);\n                    return;\n                }\n                throw new Error('Error al cargar los cuestionarios');\n            }\n            const data = await response.json();\n            setCuestionarios(data || []);\n        } catch (err) {\n            console.error('Error fetching cuestionarios:', err);\n            // Don't show error to user, just set empty array\n            setCuestionarios([]);\n        }\n    };\n    // Load cuestionarios on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CuestionarioComponent.useEffect\": ()=>{\n            fetchCuestionarios();\n        }\n    }[\"CuestionarioComponent.useEffect\"], []);\n    // New structure functions\n    const addCuestionario = ()=>{\n        const newCuestionario = {\n            id: Date.now().toString(),\n            pregunta: '',\n            respuesta_correcta: 'A',\n            explicacion_general: '',\n            opciones: [\n                {\n                    id: '1',\n                    opcion: 'A. ',\n                    es_correcta: true,\n                    explicacion: ''\n                },\n                {\n                    id: '2',\n                    opcion: 'B. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '3',\n                    opcion: 'C. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '4',\n                    opcion: 'D. ',\n                    es_correcta: false,\n                    explicacion: ''\n                }\n            ]\n        };\n        setCuestionariosData([\n            ...cuestionariosData,\n            newCuestionario\n        ]);\n    };\n    const removeCuestionario = (id)=>{\n        setCuestionariosData(cuestionariosData.filter((c)=>c.id !== id));\n    };\n    const updateCuestionario = (id, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === id ? {\n                ...c,\n                [field]: value\n            } : c));\n    };\n    const updateOpcion = (cuestionarioId, opcionId, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                opciones: c.opciones.map((o)=>o.id === opcionId ? {\n                        ...o,\n                        [field]: value\n                    } : o)\n            } : c));\n    };\n    const setCorrectAnswer = (cuestionarioId, respuestaCorrecta)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                respuesta_correcta: respuestaCorrecta,\n                opciones: c.opciones.map((o)=>({\n                        ...o,\n                        es_correcta: o.opcion.startsWith(respuestaCorrecta + '.')\n                    }))\n            } : c));\n    };\n    // CRUD functions for existing cuestionarios\n    const updateCuestionarioInDB = async (cuestionarioId, updates)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                throw new Error('Error al actualizar el cuestionario');\n            }\n            const updatedCuestionario = await response.json();\n            // Update local state\n            setCuestionarios(cuestionarios.map((c)=>c.id === cuestionarioId ? updatedCuestionario : c));\n            return updatedCuestionario;\n        } catch (err) {\n            console.error('Error updating cuestionario:', err);\n            setError('Error al actualizar el cuestionario');\n            throw err;\n        }\n    };\n    const deleteCuestionarioFromDB = async (cuestionarioId)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Error al eliminar el cuestionario');\n            }\n            // Update local state\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            if (selectedCuestionarioId === cuestionarioId) {\n                setSelectedCuestionario(null);\n                setSelectedCuestionarioId(null);\n                setShowDetailView(false);\n            }\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Image upload functions\n    const uploadImage = async (file, type, cuestionarioId, opcionId)=>{\n        const uploadId = \"\".concat(cuestionarioId, \"-\").concat(type, \"-\").concat(opcionId || 'pregunta');\n        setUploadingImages((prev)=>new Set([\n                ...prev,\n                uploadId\n            ]));\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('content_type', 'cuestionarios');\n            formData.append('content_id', cuestionarioId.toString());\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/upload-image\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error('Error al subir la imagen');\n            }\n            const result = await response.json();\n            // Update the cuestionario with the new image URL\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = result.url;\n                    pregunta.imagen_path = result.path;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = result.url;\n                        opcion.imagen_path = result.path;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n            return result;\n        } catch (err) {\n            console.error('Error uploading image:', err);\n            setError('Error al subir la imagen');\n            throw err;\n        } finally{\n            setUploadingImages((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(uploadId);\n                return newSet;\n            });\n        }\n    };\n    const removeImage = async (cuestionarioId, type, opcionId)=>{\n        try {\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = undefined;\n                    pregunta.imagen_path = undefined;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = undefined;\n                        opcion.imagen_path = undefined;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n        } catch (err) {\n            console.error('Error removing image:', err);\n            setError('Error al eliminar la imagen');\n        }\n    };\n    const handleDelete = async (cuestionarioId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar este cuestionario?')) return;\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar el cuestionario');\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Handle JSON content submission\n    const handleJsonSubmit = async ()=>{\n        if (!jsonContent.trim()) {\n            setError('Por favor ingrese el contenido JSON');\n            return;\n        }\n        if (!sharedData.especialidad || !sharedData.tema) {\n            setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            // Validate JSON format first\n            let parsedJson;\n            try {\n                parsedJson = JSON.parse(jsonContent);\n            } catch (parseError) {\n                throw new Error('Formato JSON inválido');\n            }\n            // Validate structure\n            if (!Array.isArray(parsedJson) || parsedJson.length === 0) {\n                throw new Error('El JSON debe contener una lista de cuestionarios');\n            }\n            // Validate required fields\n            const requiredFields = [\n                'id',\n                'pregunta',\n                'opciones',\n                'respuesta_correcta',\n                'explicacion_general'\n            ];\n            for (const cuestionario of parsedJson){\n                for (const field of requiredFields){\n                    if (!(field in cuestionario)) {\n                        throw new Error(\"Campo requerido faltante: \".concat(field));\n                    }\n                }\n            }\n            const formData = new FormData();\n            formData.append('json_content', jsonContent);\n            formData.append('especialidad', sharedData.especialidad);\n            formData.append('sistema', 'TuResiBo');\n            formData.append('tema', sharedData.tema);\n            if (sharedData.titulo) {\n                formData.append('titulo', sharedData.titulo);\n            }\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/json\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al procesar el contenido JSON');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                setError(null);\n                setJsonContent('');\n                fetchCuestionarios();\n            } else {\n                throw new Error(result.message || 'Error al procesar el contenido JSON');\n            }\n        } catch (err) {\n            console.error('Error processing JSON:', err);\n            setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // Enhanced detailed view component with inline editing\n    const DetailedView = (param)=>{\n        let { cuestionario, cuestionarioId, isEditing, onToggleEdit } = param;\n        _s1();\n        const [localCuestionario, setLocalCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(cuestionario);\n        const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const handleSave = async ()=>{\n            setIsSaving(true);\n            try {\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: [\n                        localCuestionario\n                    ]\n                });\n                onToggleEdit();\n            } catch (err) {\n                console.error('Error saving:', err);\n            } finally{\n                setIsSaving(false);\n            }\n        };\n        const handleImageUpload = async (file, type, opcionId)=>{\n            try {\n                await uploadImage(file, type, cuestionarioId, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error uploading image:', err);\n            }\n        };\n        const handleImageRemove = async (type, opcionId)=>{\n            try {\n                await removeImage(cuestionarioId, type, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error removing image:', err);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Cuestionario Detallado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        disabled: isSaving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: isSaving ? 'Guardando...' : 'Guardar'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: onToggleEdit,\n                                        variant: \"outline\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onToggleEdit,\n                                variant: \"outline\",\n                                children: \"Editar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-3\",\n                            children: \"Pregunta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.pregunta,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    pregunta: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed mb-4\",\n                            children: cuestionario.pregunta\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: cuestionario.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: cuestionario.imagen_url,\n                                        alt: \"Imagen de la pregunta\",\n                                        className: \"max-w-full h-auto rounded-lg border\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>handleImageRemove('pregunta'),\n                                        variant: \"destructive\",\n                                        size: \"sm\",\n                                        className: \"absolute top-2 right-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Agregar imagen a la pregunta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"file\",\n                                        accept: \"image/*\",\n                                        onChange: (e)=>{\n                                            var _e_target_files;\n                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                            if (file) handleImageUpload(file, 'pregunta');\n                                        },\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Opciones de Respuesta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        (isEditing ? localCuestionario.opciones : cuestionario.opciones).map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4 \".concat(opcion.es_correcta ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-white'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium \".concat(opcion.es_correcta ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-700'),\n                                            children: String.fromCharCode(65 + index)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-3\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    value: opcion.opcion,\n                                                    onChange: (e)=>{\n                                                        const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                ...o,\n                                                                opcion: e.target.value\n                                                            } : o);\n                                                        setLocalCuestionario({\n                                                            ...localCuestionario,\n                                                            opciones: updatedOpciones\n                                                        });\n                                                    },\n                                                    className: \"font-medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: opcion.opcion\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 border border-gray-200 rounded p-3\",\n                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                        value: opcion.explicacion,\n                                                        onChange: (e)=>{\n                                                            const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                    ...o,\n                                                                    explicacion: e.target.value\n                                                                } : o);\n                                                            setLocalCuestionario({\n                                                                ...localCuestionario,\n                                                                opciones: updatedOpciones\n                                                            });\n                                                        },\n                                                        rows: 2,\n                                                        className: \"text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Explicaci\\xf3n:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            opcion.explicacion\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: opcion.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: opcion.imagen_url,\n                                                                alt: \"Imagen opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                className: \"max-w-full h-auto rounded border\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: ()=>handleImageRemove('opcion', opcion.id),\n                                                                variant: \"destructive\",\n                                                                size: \"sm\",\n                                                                className: \"absolute top-2 right-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 23\n                                                    }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Agregar imagen a la opci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleImageUpload(file, 'opcion', opcion.id);\n                                                                },\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"correct-answer-\".concat(cuestionarioId),\n                                                            checked: opcion.es_correcta,\n                                                            onChange: ()=>{\n                                                                const letter = String.fromCharCode(65 + index);\n                                                                const updatedOpciones = localCuestionario.opciones.map((o)=>({\n                                                                        ...o,\n                                                                        es_correcta: o.id === opcion.id\n                                                                    }));\n                                                                setLocalCuestionario({\n                                                                    ...localCuestionario,\n                                                                    opciones: updatedOpciones,\n                                                                    respuesta_correcta: letter\n                                                                });\n                                                            },\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm\",\n                                                            children: \"Respuesta correcta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"\".concat(cuestionarioId, \"-opcion-\").concat(opcion.id, \"-\").concat(index), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-green-900 mb-2\",\n                            children: \"Respuesta Correcta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800\",\n                            children: [\n                                \"Opci\\xf3n \",\n                                cuestionario.respuesta_correcta\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-yellow-900 mb-3\",\n                            children: \"Explicaci\\xf3n General\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.explicacion_general,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    explicacion_general: e.target.value\n                                }),\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed\",\n                            children: cuestionario.explicacion_general\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 441,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(DetailedView, \"M9DyeJGnuD9oeGWL64ONYbhHnEY=\");\n    // Main component render\n    if (showDetailView && selectedCuestionario && selectedCuestionarioId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>{\n                        setShowDetailView(false);\n                        setSelectedCuestionario(null);\n                        setSelectedCuestionarioId(null);\n                        setEditingCuestionario(null);\n                    },\n                    variant: \"outline\",\n                    className: \"mb-4\",\n                    children: \"← Volver a la lista\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedView, {\n                    cuestionario: selectedCuestionario,\n                    cuestionarioId: selectedCuestionarioId,\n                    isEditing: editingCuestionario === selectedCuestionarioId,\n                    onToggleEdit: ()=>{\n                        if (editingCuestionario === selectedCuestionarioId) {\n                            setEditingCuestionario(null);\n                        } else {\n                            setEditingCuestionario(selectedCuestionarioId);\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Nuevo Cuestionario\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-blue-900\",\n                                                    children: \"Crear desde JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mb-3\",\n                                            children: \"Pega el contenido JSON para crear m\\xfaltiples cuestionarios de una vez.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"json-content\",\n                                                    placeholder: \"Pega aqu\\xed el contenido JSON...\",\n                                                    value: jsonContent,\n                                                    onChange: (e)=>setJsonContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"font-mono text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: handleJsonSubmit,\n                                                    disabled: !jsonContent.trim() || isUploading,\n                                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUploading ? 'Procesando...' : 'Crear desde JSON'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            className: \"mt-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    className: \"text-sm text-blue-700 cursor-pointer hover:text-blue-800\",\n                                                    children: \"Ver formato JSON requerido\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        children: '[\\n  {\\n    \"id\": \"1\",\\n    \"pregunta\": \"\\xbfCu\\xe1l es el tratamiento de primera l\\xednea para la hipertensi\\xf3n arterial?\",\\n    \"opciones\": [\\n      {\\n        \"opcion\": \"A. Inhibidores de la ECA (IECA)\",\\n        \"es_correcta\": true,\\n        \"explicacion\": \"Los IECA son considerados tratamiento de primera l\\xednea...\"\\n      },\\n      {\\n        \"opcion\": \"B. Betabloqueadores\",\\n        \"es_correcta\": false,\\n        \"explicacion\": \"Los betabloqueadores no son la primera opci\\xf3n...\"\\n      }\\n    ],\\n    \"respuesta_correcta\": \"A\",\\n    \"explicacion_general\": \"El manejo inicial de la hipertensi\\xf3n sigue las gu\\xedas...\"\\n  }\\n]'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-full border-t\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs uppercase\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-white px-2 text-gray-500\",\n                                                children: \"O crear manualmente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 border border-green-200 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-green-900\",\n                                                    children: \"Crear Cuestionario\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Cuestionarios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: addCuestionario,\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Agregar Cuestionario\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 17\n                                                }, this),\n                                                cuestionariosData.map((cuestionario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        className: \"p-4 bg-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"Cuestionario \",\n                                                                                cuestionario.id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeCuestionario(cuestionario.id),\n                                                                            className: \"text-red-600 hover:text-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Pregunta\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 822,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Escriba la pregunta...\",\n                                                                            value: cuestionario.pregunta,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'pregunta', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Opciones\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: cuestionario.opciones.map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"border rounded p-3 space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-sm\",\n                                                                                                    children: [\n                                                                                                        String.fromCharCode(65 + index),\n                                                                                                        \".\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 838,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    placeholder: \"Opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                                                    value: opcion.opcion,\n                                                                                                    onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'opcion', e.target.value),\n                                                                                                    className: \"flex-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 841,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"radio\",\n                                                                                                    name: \"correct-\".concat(cuestionario.id),\n                                                                                                    checked: opcion.es_correcta,\n                                                                                                    onChange: ()=>setCorrectAnswer(cuestionario.id, String.fromCharCode(65 + index)),\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 847,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 837,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                            placeholder: \"Explicaci\\xf3n de esta opci\\xf3n...\",\n                                                                                            value: opcion.explicacion,\n                                                                                            onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'explicacion', e.target.value),\n                                                                                            rows: 2,\n                                                                                            className: \"text-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 855,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, \"manual-\".concat(cuestionario.id, \"-opcion-\").concat(opcion.id, \"-\").concat(index), true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                    lineNumber: 836,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Explicaci\\xf3n General\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 869,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Explicaci\\xf3n general del cuestionario...\",\n                                                                            value: cuestionario.explicacion_general,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'explicacion_general', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 870,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, cuestionario.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                cuestionariosData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        // Convert to JSON and submit\n                                                        const jsonData = JSON.stringify(cuestionariosData, null, 2);\n                                                        setJsonContent(jsonData);\n                                                        handleJsonSubmit();\n                                                    },\n                                                    disabled: isUploading,\n                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                    children: isUploading ? 'Creando...' : 'Crear Cuestionarios'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    variant: \"destructive\",\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    className: \"border-green-200 bg-green-50 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        className: \"text-green-800\",\n                                        children: \"\\xa1Cuestionarios creados exitosamente!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 705,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"text-lg\",\n                                children: [\n                                    \"Cuestionarios (\",\n                                    cuestionarios.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: cuestionarios.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"No hay cuestionarios a\\xfan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 17\n                                }, this) : cuestionarios.map((cuestionario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group border rounded-lg p-3 hover:bg-gray-50 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-sm truncate\",\n                                                            children: cuestionario.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: cuestionario.especialidad\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        cuestionario.preguntas.length,\n                                                                        \" preguntas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1 truncate\",\n                                                            children: cuestionario.tema\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>{\n                                                                if (cuestionario.preguntas.length > 0) {\n                                                                    setSelectedCuestionario(cuestionario.preguntas[0]);\n                                                                    setSelectedCuestionarioId(cuestionario.id);\n                                                                    setShowDetailView(true);\n                                                                }\n                                                            },\n                                                            className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleDelete(cuestionario.id),\n                                                            className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, cuestionario.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 918,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n        lineNumber: 703,\n        columnNumber: 5\n    }, this);\n}\n_s(CuestionarioComponent, \"DEzfLj3BJjkqzgGuC+DTDXNpRLw=\");\n_c = CuestionarioComponent;\nvar _c;\n$RefreshReg$(_c, \"CuestionarioComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/cuestionario.tsx\n"));

/***/ })

});