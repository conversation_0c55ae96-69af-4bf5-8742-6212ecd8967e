{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vpuyGpnRGjkFou8C30vySVISi+U2geFoS/CwcDPvqwM=", "__NEXT_PREVIEW_MODE_ID": "054059a64ccd8261e52262657a8fc4a4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e9186c4c66a2a4cd483cb2a6c5cf7a10d651d744cc4bd8b8bf2f10ec94e2f6c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ff9b846d1277372e4119a61c88dab8381e5d49db4971051d436cdc08d97453f3"}}}, "functions": {}, "sortedMiddleware": ["/"]}