{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XqKqLBV0sN9nnRP8pciqitBALYS1vaIx8opLgUPzFZQ=", "__NEXT_PREVIEW_MODE_ID": "717d0602ef07399fcd6620d6bb4d71fe", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e18b914a102360ba39a49a08d8e57c5d6d0b8089c4be4d1801d71d4ade3ebae0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "798e0e02c7661e573a245f58eb3b54cfa023c7d120ebb60e34f8177e69dbe164"}}}, "functions": {}, "sortedMiddleware": ["/"]}